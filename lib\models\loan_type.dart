/// Enumeration of different loan types supported by the calculator
enum LoanType {
  personal('Personal Loan'),
  mortgage('Mortgage'),
  auto('Auto Loan'),
  business('Business Loan');

  const LoanType(this.displayName);

  final String displayName;

  /// Returns the typical interest rate range for this loan type
  String get typicalRateRange {
    switch (this) {
      case LoanType.personal:
        return '6% - 36%';
      case LoanType.mortgage:
        return '3% - 8%';
      case LoanType.auto:
        return '3% - 15%';
      case LoanType.business:
        return '4% - 25%';
    }
  }

  /// Returns the typical term range for this loan type
  String get typicalTermRange {
    switch (this) {
      case LoanType.personal:
        return '2 - 7 years';
      case LoanType.mortgage:
        return '15 - 30 years';
      case LoanType.auto:
        return '3 - 7 years';
      case LoanType.business:
        return '1 - 25 years';
    }
  }

  /// Returns whether down payment is typically required for this loan type
  bool get requiresDownPayment {
    switch (this) {
      case LoanType.personal:
        return false;
      case LoanType.mortgage:
        return true;
      case LoanType.auto:
        return true;
      case LoanType.business:
        return false;
    }
  }
}
