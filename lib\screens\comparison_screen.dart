import 'package:flutter/material.dart';
import '../models/loan_model.dart';
import '../utils/formatters.dart';
import '../services/loan_calculator_service.dart';

/// Screen for comparing two loan scenarios side by side
class ComparisonScreen extends StatelessWidget {
  final LoanModel loan1;
  final LoanModel loan2;

  const ComparisonScreen({
    super.key,
    required this.loan1,
    required this.loan2,
  });

  @override
  Widget build(BuildContext context) {
    final comparison = LoanCalculatorService.compareLoans(loan1, loan2);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Loan Comparison'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () => _shareComparison(context),
            tooltip: 'Share Comparison',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header cards
            Row(
              children: [
                Expanded(
                  child: _buildLoanHeaderCard(context, loan1, 'Loan A'),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildLoanHeaderCard(context, loan2, 'Loan B'),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Comparison summary
            _buildComparisonSummary(context, comparison),
            
            const SizedBox(height: 24),
            
            // Detailed comparison
            _buildDetailedComparison(context, loan1, loan2),
            
            const SizedBox(height: 24),
            
            // Recommendation
            _buildRecommendation(context, loan1, loan2, comparison),
          ],
        ),
      ),
    );
  }

  Widget _buildLoanHeaderCard(BuildContext context, LoanModel loan, String label) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.labelLarge?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              loan.name,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              loan.loanType.displayName,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonSummary(BuildContext context, Map<String, dynamic> comparison) {
    final loan1Data = comparison['loan1'] as Map<String, dynamic>;
    final loan2Data = comparison['loan2'] as Map<String, dynamic>;
    final differences = comparison['differences'] as Map<String, dynamic>;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Comparison',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            _buildComparisonRow(
              context,
              'Monthly Payment',
              Formatters.formatCurrency(loan1Data['monthlyPayment'], loan1.currency),
              Formatters.formatCurrency(loan2Data['monthlyPayment'], loan2.currency),
              Formatters.formatDifference(differences['monthlyPayment'], loan1.currency),
            ),
            
            const Divider(),
            
            _buildComparisonRow(
              context,
              'Total Interest',
              Formatters.formatCurrency(loan1Data['totalInterest'], loan1.currency),
              Formatters.formatCurrency(loan2Data['totalInterest'], loan2.currency),
              Formatters.formatDifference(differences['totalInterest'], loan1.currency),
            ),
            
            const Divider(),
            
            _buildComparisonRow(
              context,
              'Total Amount',
              Formatters.formatCurrency(loan1Data['totalAmount'], loan1.currency),
              Formatters.formatCurrency(loan2Data['totalAmount'], loan2.currency),
              Formatters.formatDifference(differences['totalAmount'], loan1.currency),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparisonRow(
    BuildContext context,
    String label,
    String value1,
    String value2,
    String difference,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.titleSmall,
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: Text(
                  'A: $value1',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
              Expanded(
                child: Text(
                  'B: $value2',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
          const SizedBox(height: 2),
          Text(
            'Difference: $difference',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: difference.startsWith('+')
                  ? Theme.of(context).colorScheme.error
                  : Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedComparison(BuildContext context, LoanModel loan1, LoanModel loan2) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Detailed Comparison',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            
            _buildDetailRow(context, 'Loan Amount', 
                Formatters.formatCurrency(loan1.loanAmount, loan1.currency),
                Formatters.formatCurrency(loan2.loanAmount, loan2.currency)),
            
            _buildDetailRow(context, 'Interest Rate', 
                Formatters.formatPercentage(loan1.annualInterestRate),
                Formatters.formatPercentage(loan2.annualInterestRate)),
            
            _buildDetailRow(context, 'Loan Term', 
                Formatters.formatLoanTerm(loan1.termInMonths),
                Formatters.formatLoanTerm(loan2.termInMonths)),
            
            if (loan1.downPayment > 0 || loan2.downPayment > 0)
              _buildDetailRow(context, 'Down Payment', 
                  Formatters.formatCurrency(loan1.downPayment, loan1.currency),
                  Formatters.formatCurrency(loan2.downPayment, loan2.currency)),
            
            _buildDetailRow(context, 'Principal Amount', 
                Formatters.formatCurrency(loan1.principalAmount, loan1.currency),
                Formatters.formatCurrency(loan2.principalAmount, loan2.currency)),
            
            _buildDetailRow(context, 'Currency', 
                loan1.currency.displayName,
                loan2.currency.displayName),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(BuildContext context, String label, String value1, String value2) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Expanded(
            child: Text(
              value1,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value2,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendation(
    BuildContext context,
    LoanModel loan1,
    LoanModel loan2,
    Map<String, dynamic> comparison,
  ) {
    final differences = comparison['differences'] as Map<String, dynamic>;
    final monthlyDiff = differences['monthlyPayment'] as double;
    final totalInterestDiff = differences['totalInterest'] as double;
    
    String recommendation;
    String reason;
    IconData icon;
    Color color;
    
    if (monthlyDiff.abs() < 10 && totalInterestDiff.abs() < 100) {
      recommendation = 'Both loans are very similar';
      reason = 'The difference in monthly payment and total interest is minimal. Consider other factors like lender reputation and terms.';
      icon = Icons.balance;
      color = Theme.of(context).colorScheme.primary;
    } else if (totalInterestDiff < 0) {
      recommendation = 'Loan A is better overall';
      reason = 'Loan A has lower total interest cost of ${Formatters.formatCurrency(totalInterestDiff.abs(), loan1.currency)}.';
      icon = Icons.thumb_up;
      color = Theme.of(context).colorScheme.primary;
    } else {
      recommendation = 'Loan B is better overall';
      reason = 'Loan B has lower total interest cost of ${Formatters.formatCurrency(totalInterestDiff, loan1.currency)}.';
      icon = Icons.thumb_up;
      color = Theme.of(context).colorScheme.primary;
    }

    return Card(
      color: color.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 8),
                Text(
                  'Recommendation',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              recommendation,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              reason,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 12),
            Text(
              'Note: This recommendation is based purely on financial calculations. Consider other factors such as lender reputation, customer service, and loan terms.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _shareComparison(BuildContext context) {
    // TODO: Implement share comparison functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Share comparison feature coming soon')),
    );
  }
}
