import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/loan_model.dart';
import '../models/currency.dart';

/// Service for persisting loan data and app settings
class StorageService {
  static const String _loansKey = 'saved_loans';
  static const String _settingsKey = 'app_settings';
  static const String _defaultCurrencyKey = 'default_currency';

  /// Saves a loan to local storage
  static Future<bool> saveLoan(LoanModel loan) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLoans = await getSavedLoans();
      
      // Remove existing loan with same ID if it exists
      savedLoans.removeWhere((existingLoan) => existingLoan.id == loan.id);
      
      // Add the new/updated loan
      savedLoans.add(loan);
      
      // Convert to JSON and save
      final loansJson = savedLoans.map((loan) => loan.toJson()).toList();
      final jsonString = jsonEncode(loansJson);
      
      return await prefs.setString(_loansKey, jsonString);
    } catch (e) {
      print('Error saving loan: $e');
      return false;
    }
  }

  /// Retrieves all saved loans from local storage
  static Future<List<LoanModel>> getSavedLoans() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_loansKey);
      
      if (jsonString == null || jsonString.isEmpty) {
        return [];
      }
      
      final List<dynamic> loansJson = jsonDecode(jsonString);
      return loansJson
          .map((json) => LoanModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      print('Error loading saved loans: $e');
      return [];
    }
  }

  /// Deletes a loan from local storage
  static Future<bool> deleteLoan(String loanId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLoans = await getSavedLoans();
      
      // Remove the loan with the specified ID
      savedLoans.removeWhere((loan) => loan.id == loanId);
      
      // Save the updated list
      final loansJson = savedLoans.map((loan) => loan.toJson()).toList();
      final jsonString = jsonEncode(loansJson);
      
      return await prefs.setString(_loansKey, jsonString);
    } catch (e) {
      print('Error deleting loan: $e');
      return false;
    }
  }

  /// Clears all saved loans
  static Future<bool> clearAllLoans() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_loansKey);
    } catch (e) {
      print('Error clearing loans: $e');
      return false;
    }
  }

  /// Saves the default currency setting
  static Future<bool> setDefaultCurrency(Currency currency) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_defaultCurrencyKey, currency.code);
    } catch (e) {
      print('Error saving default currency: $e');
      return false;
    }
  }

  /// Retrieves the default currency setting
  static Future<Currency> getDefaultCurrency() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currencyCode = prefs.getString(_defaultCurrencyKey);
      
      if (currencyCode == null) {
        return Currency.usd; // Default to USD
      }
      
      // Find the currency by code
      for (final currency in Currency.values) {
        if (currency.code == currencyCode) {
          return currency;
        }
      }
      
      return Currency.usd; // Fallback to USD if not found
    } catch (e) {
      print('Error loading default currency: $e');
      return Currency.usd;
    }
  }

  /// Saves app settings
  static Future<bool> saveSettings(Map<String, dynamic> settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(settings);
      return await prefs.setString(_settingsKey, jsonString);
    } catch (e) {
      print('Error saving settings: $e');
      return false;
    }
  }

  /// Retrieves app settings
  static Future<Map<String, dynamic>> getSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_settingsKey);
      
      if (jsonString == null || jsonString.isEmpty) {
        return _getDefaultSettings();
      }
      
      final settings = jsonDecode(jsonString) as Map<String, dynamic>;
      
      // Merge with default settings to ensure all keys exist
      final defaultSettings = _getDefaultSettings();
      defaultSettings.addAll(settings);
      
      return defaultSettings;
    } catch (e) {
      print('Error loading settings: $e');
      return _getDefaultSettings();
    }
  }

  /// Returns default app settings
  static Map<String, dynamic> _getDefaultSettings() {
    return {
      'defaultCurrency': Currency.usd.code,
      'showDetailedResults': true,
      'enableNotifications': false,
      'darkMode': false,
      'autoSaveCalculations': true,
      'decimalPlaces': 2,
    };
  }

  /// Exports all saved loans as JSON string
  static Future<String> exportLoansAsJson() async {
    try {
      final savedLoans = await getSavedLoans();
      final loansJson = savedLoans.map((loan) => loan.toJson()).toList();
      return jsonEncode({
        'exportDate': DateTime.now().toIso8601String(),
        'version': '1.0',
        'loans': loansJson,
      });
    } catch (e) {
      print('Error exporting loans: $e');
      return '{}';
    }
  }

  /// Imports loans from JSON string
  static Future<bool> importLoansFromJson(String jsonString) async {
    try {
      final data = jsonDecode(jsonString) as Map<String, dynamic>;
      final loansData = data['loans'] as List<dynamic>;
      
      final loans = loansData
          .map((json) => LoanModel.fromJson(json as Map<String, dynamic>))
          .toList();
      
      // Save each loan
      for (final loan in loans) {
        await saveLoan(loan);
      }
      
      return true;
    } catch (e) {
      print('Error importing loans: $e');
      return false;
    }
  }

  /// Gets the total number of saved loans
  static Future<int> getSavedLoansCount() async {
    final loans = await getSavedLoans();
    return loans.length;
  }

  /// Checks if a loan with the given ID exists
  static Future<bool> loanExists(String loanId) async {
    final loans = await getSavedLoans();
    return loans.any((loan) => loan.id == loanId);
  }

  /// Gets a specific loan by ID
  static Future<LoanModel?> getLoanById(String loanId) async {
    final loans = await getSavedLoans();
    try {
      return loans.firstWhere((loan) => loan.id == loanId);
    } catch (e) {
      return null;
    }
  }
}
