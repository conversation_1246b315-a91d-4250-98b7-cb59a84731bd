// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Bank Loan Calculator';

  @override
  String get calculator => 'Calculator';

  @override
  String get savedLoans => 'Saved Loans';

  @override
  String get settings => 'Settings';

  @override
  String get loanCalculator => 'Loan Calculator';

  @override
  String get loanDetails => 'Loan Details';

  @override
  String get loanName => 'Loan Name';

  @override
  String get enterLoanName => 'Enter a name for this loan';

  @override
  String get pleaseEnterLoanName => 'Please enter a loan name';

  @override
  String get loanType => 'Loan Type';

  @override
  String get currency => 'Currency';

  @override
  String get loanAmount => 'Loan Amount';

  @override
  String get enterLoanAmount => 'Enter loan amount';

  @override
  String get pleaseEnterLoanAmount => 'Please enter loan amount';

  @override
  String get pleaseEnterValidAmount => 'Please enter a valid amount';

  @override
  String get annualInterestRate => 'Annual Interest Rate';

  @override
  String get enterInterestRate => 'Enter interest rate';

  @override
  String get pleaseEnterInterestRate => 'Please enter interest rate';

  @override
  String get pleaseEnterValidRate => 'Please enter a valid rate (0-100%)';

  @override
  String get loanTerm => 'Loan Term';

  @override
  String get enterLoanTerm => 'Enter loan term';

  @override
  String get pleaseEnterLoanTerm => 'Please enter loan term';

  @override
  String get pleaseEnterValidTerm => 'Please enter a valid term';

  @override
  String get years => 'Years';

  @override
  String get months => 'Months';

  @override
  String get downPayment => 'Down Payment (Optional)';

  @override
  String get enterDownPayment => 'Enter down payment';

  @override
  String get pleaseEnterValidDownPayment => 'Please enter a valid down payment';

  @override
  String get downPaymentMustBeLess =>
      'Down payment must be less than loan amount';

  @override
  String get calculationResults => 'Calculation Results';

  @override
  String get monthlyPayment => 'Monthly Payment';

  @override
  String get principalAmount => 'Principal Amount';

  @override
  String get totalInterestPaid => 'Total Interest Paid';

  @override
  String get totalAmountPaid => 'Total Amount Paid';

  @override
  String get paymentBreakdown => 'Payment Breakdown';

  @override
  String get principal => 'Principal';

  @override
  String get interest => 'Interest';

  @override
  String get viewDetails => 'View Details';

  @override
  String get saveLoan => 'Save Loan';

  @override
  String get duplicate => 'Duplicate';

  @override
  String get newLoan => 'New Loan';

  @override
  String get loanSavedSuccessfully => 'Loan saved successfully!';

  @override
  String get failedToSaveLoan => 'Failed to save loan';

  @override
  String get loanDuplicated => 'Loan duplicated';

  @override
  String get newLoanCreated => 'New loan created';

  @override
  String get personalLoan => 'Personal Loan';

  @override
  String get mortgage => 'Mortgage';

  @override
  String get autoLoan => 'Auto Loan';

  @override
  String get businessLoan => 'Business Loan';

  @override
  String get noSavedLoans => 'No Saved Loans';

  @override
  String get calculateLoanAndSave =>
      'Calculate a loan and save it to see it here';

  @override
  String get compareLoans => 'Compare Loans';

  @override
  String get clearAll => 'Clear All';

  @override
  String get exportAll => 'Export All';

  @override
  String get delete => 'Delete';

  @override
  String get deleteLoan => 'Delete Loan';

  @override
  String get areYouSureDeleteLoan =>
      'Are you sure you want to delete this loan?';

  @override
  String areYouSureDeleteLoans(int count) {
    return 'Are you sure you want to delete $count loans?';
  }

  @override
  String get cancel => 'Cancel';

  @override
  String get currencyAndDisplay => 'Currency & Display';

  @override
  String get defaultCurrency => 'Default Currency';

  @override
  String get decimalPlaces => 'Decimal Places';

  @override
  String get display => 'Display';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get useDarkTheme => 'Use dark theme';

  @override
  String get detailedResults => 'Detailed Results';

  @override
  String get showDetailedResults => 'Show detailed calculation results';

  @override
  String get calculations => 'Calculations';

  @override
  String get autoSaveCalculations => 'Auto-save Calculations';

  @override
  String get automaticallySave => 'Automatically save loan calculations';

  @override
  String get dataManagement => 'Data Management';

  @override
  String get exportData => 'Export Data';

  @override
  String get exportAllSavedLoans => 'Export all saved loans';

  @override
  String get importData => 'Import Data';

  @override
  String get importSavedLoans => 'Import saved loans';

  @override
  String get clearAllData => 'Clear All Data';

  @override
  String get deleteAllSavedLoans => 'Delete all saved loans and settings';

  @override
  String get about => 'About';

  @override
  String get appVersion => 'App Version';

  @override
  String get helpAndSupport => 'Help & Support';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get resetToDefaults => 'Reset to Defaults';

  @override
  String get language => 'Language';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get english => 'English';

  @override
  String get arabic => 'العربية';

  @override
  String get summary => 'Summary';

  @override
  String get schedule => 'Schedule';

  @override
  String get quickStatistics => 'Quick Statistics';

  @override
  String get firstPayment => 'First Payment';

  @override
  String get finalPayment => 'Final Payment';

  @override
  String get interestRate => 'Interest Rate';

  @override
  String get totalPayments => 'Total Payments';

  @override
  String get yearlySummary => 'Yearly Summary';

  @override
  String get shareResults => 'Share Results';

  @override
  String get exportAsPdf => 'Export as PDF';

  @override
  String get monthly => 'Monthly';

  @override
  String get yearly => 'Yearly';

  @override
  String get allYears => 'All Years';

  @override
  String get paymentNumber => 'Payment #';

  @override
  String get date => 'Date';

  @override
  String get payment => 'Payment';

  @override
  String get balance => 'Balance';

  @override
  String get payments => 'Payments';

  @override
  String get totalPaid => 'Total Paid';

  @override
  String get endBalance => 'End Balance';

  @override
  String get loanComparison => 'Loan Comparison';

  @override
  String get quickComparison => 'Quick Comparison';

  @override
  String get difference => 'Difference';

  @override
  String get detailedComparison => 'Detailed Comparison';

  @override
  String get recommendation => 'Recommendation';

  @override
  String get bothLoansAreSimilar => 'Both loans are very similar';

  @override
  String get loanAIsBetter => 'Loan A is better overall';

  @override
  String get loanBIsBetter => 'Loan B is better overall';

  @override
  String get note => 'Note';

  @override
  String get recommendationNote =>
      'This recommendation is based purely on financial calculations. Consider other factors such as lender reputation, customer service, and loan terms.';

  @override
  String get created => 'Created';

  @override
  String get amount => 'Amount';

  @override
  String get rate => 'Rate';

  @override
  String get term => 'Term';

  @override
  String get perMonth => 'per month';

  @override
  String get selectCurrency => 'Select Currency';

  @override
  String get close => 'Close';

  @override
  String get gotIt => 'Got it';

  @override
  String get howToUse => 'How to Use';

  @override
  String get howToUseContent =>
      '1. Select your loan type\n2. Enter the loan amount\n3. Set the annual interest rate\n4. Choose the loan term\n5. Add down payment if applicable\n6. View calculated results\n7. Save or export your calculation';

  @override
  String get tips => 'Tips';

  @override
  String get tipsContent =>
      '• Use the detailed view for amortization schedule\n• Save multiple loan scenarios for comparison\n• Export results as PDF for sharing';

  @override
  String get help => 'Help';

  @override
  String get bankLoanCalculator => 'Bank Loan Calculator';

  @override
  String get helpContent =>
      'This app helps you calculate loan payments, compare different loan options, and understand the total cost of borrowing.';

  @override
  String get features => 'Features';

  @override
  String get featuresContent =>
      '• Calculate monthly payments\n• View amortization schedules\n• Compare multiple loans\n• Save and manage loan calculations\n• Export results as PDF';

  @override
  String get support => 'For support, please contact <NAME_EMAIL>';

  @override
  String get privacyPolicyContent =>
      'This app stores loan calculation data locally on your device. No personal or financial information is transmitted to external servers. All calculations are performed locally and your data remains private.';

  @override
  String get resetSettings => 'Reset Settings';

  @override
  String get resetSettingsConfirm =>
      'Reset all settings to their default values?';

  @override
  String get reset => 'Reset';

  @override
  String get settingsResetToDefaults => 'Settings reset to defaults';

  @override
  String get clearAllLoans => 'Clear All Loans';

  @override
  String get clearAllLoansConfirm =>
      'Are you sure you want to delete all saved loans? This action cannot be undone.';

  @override
  String get allDataCleared => 'All data cleared successfully';

  @override
  String get exportFeatureComingSoon => 'Export feature coming soon';

  @override
  String get importFeatureComingSoon => 'Import feature coming soon';

  @override
  String get shareComparisonComingSoon =>
      'Share comparison feature coming soon';

  @override
  String get exportAllFeatureComingSoon => 'Export all feature coming soon';

  @override
  String get pdfExportedSuccessfully => 'PDF exported successfully';

  @override
  String get errorExportingPdf => 'Error exporting PDF';

  @override
  String get errorSharingResults => 'Error sharing results';

  @override
  String get errorComparingLoans => 'Error comparing loans';

  @override
  String get select2LoansToCompare => 'Select 2 loans to compare';

  @override
  String get selected => 'selected';

  @override
  String get compare => 'Compare';

  @override
  String get deleteSelected => 'Delete Selected';

  @override
  String get loanA => 'Loan A';

  @override
  String get loanB => 'Loan B';

  @override
  String get copy => 'Copy';
}
