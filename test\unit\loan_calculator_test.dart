import 'package:flutter_test/flutter_test.dart';
import 'package:bank_loan_calculator/models/loan_model.dart';
import 'package:bank_loan_calculator/models/loan_type.dart';
import 'package:bank_loan_calculator/models/currency.dart';
import 'package:bank_loan_calculator/services/loan_calculator_service.dart';

void main() {
  group('LoanModel Tests', () {
    test('should calculate monthly payment correctly', () {
      final loan = LoanModel(
        id: '1',
        name: 'Test Loan',
        loanType: LoanType.personal,
        loanAmount: 10000,
        annualInterestRate: 5.0,
        termInMonths: 12,
        downPayment: 0,
        currency: Currency.usd,
        createdAt: DateTime.now(),
      );

      expect(loan.monthlyPayment, closeTo(856.07, 0.01));
    });

    test('should calculate total interest correctly', () {
      final loan = LoanModel(
        id: '1',
        name: 'Test Loan',
        loanType: LoanType.personal,
        loanAmount: 10000,
        annualInterestRate: 5.0,
        termInMonths: 12,
        downPayment: 0,
        currency: Currency.usd,
        createdAt: DateTime.now(),
      );

      expect(loan.totalInterestPaid, closeTo(272.90, 0.01));
    });

    test('should handle zero interest rate', () {
      final loan = LoanModel(
        id: '1',
        name: 'Test Loan',
        loanType: LoanType.personal,
        loanAmount: 12000,
        annualInterestRate: 0.0,
        termInMonths: 12,
        downPayment: 0,
        currency: Currency.usd,
        createdAt: DateTime.now(),
      );

      expect(loan.monthlyPayment, equals(1000.0));
      expect(loan.totalInterestPaid, equals(0.0));
    });

    test('should calculate principal amount with down payment', () {
      final loan = LoanModel(
        id: '1',
        name: 'Test Loan',
        loanType: LoanType.auto,
        loanAmount: 20000,
        annualInterestRate: 4.0,
        termInMonths: 60,
        downPayment: 5000,
        currency: Currency.usd,
        createdAt: DateTime.now(),
      );

      expect(loan.principalAmount, equals(15000.0));
    });

    test('should validate loan parameters', () {
      final validLoan = LoanModel(
        id: '1',
        name: 'Valid Loan',
        loanType: LoanType.personal,
        loanAmount: 10000,
        annualInterestRate: 5.0,
        termInMonths: 12,
        downPayment: 0,
        currency: Currency.usd,
        createdAt: DateTime.now(),
      );

      final invalidLoan = LoanModel(
        id: '2',
        name: 'Invalid Loan',
        loanType: LoanType.personal,
        loanAmount: 0,
        annualInterestRate: 5.0,
        termInMonths: 12,
        downPayment: 0,
        currency: Currency.usd,
        createdAt: DateTime.now(),
      );

      expect(validLoan.isValid, isTrue);
      expect(invalidLoan.isValid, isFalse);
    });
  });

  group('LoanCalculatorService Tests', () {
    test('should generate correct amortization schedule', () {
      final loan = LoanModel(
        id: '1',
        name: 'Test Loan',
        loanType: LoanType.personal,
        loanAmount: 1000,
        annualInterestRate: 12.0,
        termInMonths: 12,
        downPayment: 0,
        currency: Currency.usd,
        createdAt: DateTime.now(),
      );

      final schedule = LoanCalculatorService.calculateAmortizationSchedule(
        loan,
      );

      expect(schedule.payments.length, equals(12));
      expect(schedule.totalPayments, equals(12));
      expect(schedule.payments.first.paymentNumber, equals(1));
      expect(schedule.payments.last.paymentNumber, equals(12));
      expect(schedule.payments.last.remainingBalance, closeTo(0.0, 0.01));
    });

    test('should calculate loan amount for desired payment', () {
      final loanAmount = LoanCalculatorService.calculateLoanAmountForPayment(
        desiredMonthlyPayment: 500,
        annualInterestRate: 6.0,
        termInMonths: 24,
      );

      expect(loanAmount, greaterThan(0));
      expect(loanAmount, closeTo(11281.43, 1.0));
    });

    test('should compare two loans correctly', () {
      final loan1 = LoanModel(
        id: '1',
        name: 'Loan 1',
        loanType: LoanType.personal,
        loanAmount: 10000,
        annualInterestRate: 5.0,
        termInMonths: 12,
        downPayment: 0,
        currency: Currency.usd,
        createdAt: DateTime.now(),
      );

      final loan2 = LoanModel(
        id: '2',
        name: 'Loan 2',
        loanType: LoanType.personal,
        loanAmount: 10000,
        annualInterestRate: 6.0,
        termInMonths: 12,
        downPayment: 0,
        currency: Currency.usd,
        createdAt: DateTime.now(),
      );

      final comparison = LoanCalculatorService.compareLoans(loan1, loan2);

      expect(comparison['loan1'], isNotNull);
      expect(comparison['loan2'], isNotNull);
      expect(comparison['differences'], isNotNull);

      final differences = comparison['differences'] as Map<String, dynamic>;
      expect(
        differences['monthlyPayment'],
        greaterThan(0),
      ); // Loan 2 should have higher payment
    });

    test('should handle edge cases in amortization calculation', () {
      // Very small loan amount
      final smallLoan = LoanModel(
        id: '1',
        name: 'Small Loan',
        loanType: LoanType.personal,
        loanAmount: 1,
        annualInterestRate: 5.0,
        termInMonths: 1,
        downPayment: 0,
        currency: Currency.usd,
        createdAt: DateTime.now(),
      );

      final schedule = LoanCalculatorService.calculateAmortizationSchedule(
        smallLoan,
      );
      expect(schedule.payments.length, equals(1));
      expect(schedule.payments.first.remainingBalance, closeTo(0.0, 0.01));
    });
  });

  group('Currency Tests', () {
    test('should format amounts correctly for different currencies', () {
      expect(Currency.usd.formatAmount(1234.56), equals('\$1234.56'));
      expect(Currency.eur.formatAmount(1234.56), equals('€1234.56'));
      expect(
        Currency.jpy.formatAmount(1234.56),
        equals('¥1235'),
      ); // No decimals for JPY
    });

    test('should have correct decimal places', () {
      expect(Currency.usd.decimalPlaces, equals(2));
      expect(Currency.eur.decimalPlaces, equals(2));
      expect(Currency.jpy.decimalPlaces, equals(0));
    });
  });

  group('LoanType Tests', () {
    test('should have correct display names', () {
      expect(LoanType.personal.displayName, equals('Personal Loan'));
      expect(LoanType.mortgage.displayName, equals('Mortgage'));
      expect(LoanType.auto.displayName, equals('Auto Loan'));
      expect(LoanType.business.displayName, equals('Business Loan'));
    });

    test('should have correct down payment requirements', () {
      expect(LoanType.personal.requiresDownPayment, isFalse);
      expect(LoanType.mortgage.requiresDownPayment, isTrue);
      expect(LoanType.auto.requiresDownPayment, isTrue);
      expect(LoanType.business.requiresDownPayment, isFalse);
    });
  });
}
