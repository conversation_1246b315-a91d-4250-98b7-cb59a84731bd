name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.1'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Generate code
      run: flutter packages pub run build_runner build --delete-conflicting-outputs
      
    - name: Verify formatting
      run: flutter format --set-exit-if-changed .
      
    - name: Analyze code
      run: flutter analyze
      
    - name: Run tests
      run: flutter test --coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info
        fail_ci_if_error: true

  build-android:
    name: Build Android
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.1'
        channel: 'stable'
        
    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'zulu'
        java-version: '11'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Generate code
      run: flutter packages pub run build_runner build --delete-conflicting-outputs
      
    - name: Build APK
      run: flutter build apk --release
      
    - name: Upload APK artifact
      uses: actions/upload-artifact@v3
      with:
        name: android-apk
        path: build/app/outputs/flutter-apk/app-release.apk

  build-ios:
    name: Build iOS
    runs-on: macos-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.1'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Generate code
      run: flutter packages pub run build_runner build --delete-conflicting-outputs
      
    - name: Build iOS (no codesign)
      run: flutter build ios --release --no-codesign
      
    - name: Upload iOS artifact
      uses: actions/upload-artifact@v3
      with:
        name: ios-build
        path: build/ios/iphoneos/Runner.app

  build-web:
    name: Build Web
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.1'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Generate code
      run: flutter packages pub run build_runner build --delete-conflicting-outputs
      
    - name: Build Web
      run: flutter build web --release
      
    - name: Upload Web artifact
      uses: actions/upload-artifact@v3
      with:
        name: web-build
        path: build/web/

  build-windows:
    name: Build Windows
    runs-on: windows-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.8.1'
        channel: 'stable'
        
    - name: Get dependencies
      run: flutter pub get
      
    - name: Generate code
      run: flutter packages pub run build_runner build --delete-conflicting-outputs
      
    - name: Build Windows
      run: flutter build windows --release
      
    - name: Upload Windows artifact
      uses: actions/upload-artifact@v3
      with:
        name: windows-build
        path: build/windows/x64/runner/Release/

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  deploy-web:
    name: Deploy Web
    runs-on: ubuntu-latest
    needs: [test, build-web]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download Web artifact
      uses: actions/download-artifact@v3
      with:
        name: web-build
        path: build/web/
        
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: build/web/
