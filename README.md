# Bank Loan Calculator

A comprehensive mobile application built with Flutter/Dart for calculating loan payments, comparing loan scenarios, and managing financial calculations with professional-grade features.

## 🚀 Features

### Core Functionality
- **Loan Payment Calculation**: Calculate monthly payments using standard amortization formulas
- **Multiple Loan Types**: Support for personal loans, mortgages, auto loans, and business loans
- **Comprehensive Input Validation**: Real-time validation with helpful error messages
- **Currency Support**: Multiple currencies with proper formatting (USD, EUR, GBP, CAD, AUD, JPY, INR, CNY)

### Advanced Features
- **Amortization Schedule**: Detailed payment-by-payment breakdown showing principal vs interest
- **Loan Comparison**: Side-by-side comparison of multiple loan scenarios with recommendations
- **Save & Manage**: Save loan calculations for future reference with local storage
- **Export Functionality**: Export results as PDF or share via text
- **Dark Mode**: Full dark/light theme support
- **Responsive Design**: Works seamlessly on phones and tablets

### User Experience
- **Intuitive Navigation**: Bottom navigation with Cal<PERSON>tor, Saved Loans, and Settings tabs
- **Material Design**: Follows Google's Material Design 3 guidelines
- **Accessibility**: Screen reader support and proper contrast ratios
- **Offline Support**: All calculations work without internet connection

## 🛠 Technical Architecture

### State Management
- **Provider Pattern**: Clean separation of business logic and UI
- **Reactive Updates**: Real-time calculation updates as user types
- **Error Handling**: Comprehensive error states and user feedback

### Data Persistence
- **Local Storage**: SharedPreferences for settings and saved loans
- **JSON Serialization**: Robust data serialization with json_annotation
- **Data Validation**: Input validation and data integrity checks

### Code Organization
```
lib/
├── models/           # Data models (Loan, Currency, PaymentSchedule)
├── services/         # Business logic (Calculator, Storage, Export)
├── providers/        # State management (Loan, Settings)
├── screens/          # UI screens (Home, Results, Comparison, Settings)
├── widgets/          # Reusable UI components
└── utils/           # Utilities (Formatters, Validators)
```

## 🧮 Calculation Features

### Loan Calculations
- **Monthly Payment**: Standard amortization formula with compound interest
- **Total Interest**: Lifetime interest cost calculation
- **Principal vs Interest**: Breakdown of payment allocation
- **Zero Interest Handling**: Special case for interest-free loans

### Advanced Calculations
- **Reverse Calculations**: Calculate loan amount for desired payment
- **Interest Rate Solving**: Find required rate for target payment
- **Down Payment Support**: Automatic principal adjustment
- **Multiple Term Units**: Support for months and years

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Dart SDK (3.8.1 or higher)
- Android Studio / VS Code with Flutter extensions

### Installation
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bank_loan_calculator
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

## 🧪 Testing

### Run Tests
```bash
flutter test                    # Run all tests
flutter test test/unit/         # Run unit tests only
flutter test test/widget/       # Run widget tests only
```

### Test Coverage
- **Calculation Logic**: Comprehensive tests for all loan calculations
- **Data Models**: Validation and serialization tests
- **Edge Cases**: Zero interest, small amounts, long terms
- **UI Components**: Form validation and user interactions

## 📦 Dependencies

### Core Dependencies
- `provider: ^6.1.2` - State management
- `shared_preferences: ^2.2.3` - Local storage
- `intl: ^0.19.0` - Internationalization and formatting

### Export & Sharing
- `pdf: ^3.10.8` - PDF generation
- `share_plus: ^9.0.0` - Native sharing
- `path_provider: ^2.1.3` - File system access

### UI Enhancement
- `flutter_slidable: ^3.1.0` - Swipe actions for saved loans

## 🎯 Usage Examples

### Basic Loan Calculation
```dart
final loan = LoanModel(
  id: '1',
  name: 'Car Loan',
  loanType: LoanType.auto,
  loanAmount: 25000,
  annualInterestRate: 4.5,
  termInMonths: 60,
  downPayment: 5000,
  currency: Currency.usd,
  createdAt: DateTime.now(),
);

print('Monthly Payment: \${loan.monthlyPayment}'); // $372.86
print('Total Interest: \${loan.totalInterestPaid}'); // $2,371.60
```

## 📄 License

This project is licensed under the MIT License.

---

**Built with ❤️ using Flutter**
