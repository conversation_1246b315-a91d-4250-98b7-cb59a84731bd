import 'package:json_annotation/json_annotation.dart';

part 'payment_schedule.g.dart';

/// Represents a single payment in the amortization schedule
@JsonSerializable()
class PaymentScheduleItem {
  final int paymentNumber;
  final double paymentAmount;
  final double principalAmount;
  final double interestAmount;
  final double remainingBalance;
  final DateTime paymentDate;

  const PaymentScheduleItem({
    required this.paymentNumber,
    required this.paymentAmount,
    required this.principalAmount,
    required this.interestAmount,
    required this.remainingBalance,
    required this.paymentDate,
  });

  factory PaymentScheduleItem.fromJson(Map<String, dynamic> json) =>
      _$PaymentScheduleItemFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentScheduleItemToJson(this);

  @override
  String toString() {
    return 'PaymentScheduleItem(payment: $paymentNumber, amount: $paymentAmount, '
        'principal: $principalAmount, interest: $interestAmount, '
        'remaining: $remainingBalance)';
  }
}

/// Represents the complete amortization schedule for a loan
@JsonSerializable()
class PaymentSchedule {
  final List<PaymentScheduleItem> payments;
  final double totalInterestPaid;
  final double totalAmountPaid;
  final int totalPayments;

  const PaymentSchedule({
    required this.payments,
    required this.totalInterestPaid,
    required this.totalAmountPaid,
    required this.totalPayments,
  });

  factory PaymentSchedule.fromJson(Map<String, dynamic> json) =>
      _$PaymentScheduleFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentScheduleToJson(this);

  /// Returns payments for a specific year
  List<PaymentScheduleItem> getPaymentsForYear(int year) {
    return payments.where((payment) => payment.paymentDate.year == year).toList();
  }

  /// Returns the total interest paid in a specific year
  double getInterestForYear(int year) {
    return getPaymentsForYear(year)
        .fold(0.0, (sum, payment) => sum + payment.interestAmount);
  }

  /// Returns the total principal paid in a specific year
  double getPrincipalForYear(int year) {
    return getPaymentsForYear(year)
        .fold(0.0, (sum, payment) => sum + payment.principalAmount);
  }

  /// Returns all unique years in the payment schedule
  List<int> get years {
    return payments
        .map((payment) => payment.paymentDate.year)
        .toSet()
        .toList()
      ..sort();
  }

  @override
  String toString() {
    return 'PaymentSchedule(totalPayments: $totalPayments, '
        'totalInterest: $totalInterestPaid, totalAmount: $totalAmountPaid)';
  }
}
