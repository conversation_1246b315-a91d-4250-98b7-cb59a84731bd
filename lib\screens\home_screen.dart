import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/loan_provider.dart';
import '../providers/settings_provider.dart';
import '../providers/navigation_provider.dart';
import '../widgets/loan_input_form.dart';
import '../widgets/results_summary.dart';
import 'results_screen.dart';
import 'saved_loans_screen.dart';
import 'settings_screen.dart';

/// Main home screen with loan calculator interface
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    // Ensure saved loans are loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LoanProvider>().loadSavedLoans();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<NavigationProvider>(
      builder: (context, navigationProvider, child) {
        return Scaffold(
          body: IndexedStack(
            index: navigationProvider.currentTabIndex,
            children: const [
              _CalculatorTab(),
              SavedLoansScreen(),
              SettingsScreen(),
            ],
          ),
          bottomNavigationBar: BottomNavigationBar(
            currentIndex: navigationProvider.currentTabIndex,
            onTap: (index) => navigationProvider.switchToTab(index),
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.calculate),
                label: 'Calculator',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.bookmark),
                label: 'Saved Loans',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.settings),
                label: 'Settings',
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Calculator tab content
class _CalculatorTab extends StatelessWidget {
  const _CalculatorTab();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Loan Calculator'),
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showHelpDialog(context),
            tooltip: 'Help',
          ),
        ],
      ),
      body: Consumer<LoanProvider>(
        builder: (context, loanProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Error message display
                if (loanProvider.errorMessage != null)
                  Card(
                    color: Theme.of(context).colorScheme.errorContainer,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Theme.of(
                              context,
                            ).colorScheme.onErrorContainer,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              loanProvider.errorMessage!,
                              style: TextStyle(
                                color: Theme.of(
                                  context,
                                ).colorScheme.onErrorContainer,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                const SizedBox(height: 16),

                // Loan input form
                const LoanInputForm(),

                const SizedBox(height: 24),

                // Results summary
                if (loanProvider.hasValidLoan) ...[
                  const ResultsSummary(),
                  const SizedBox(height: 16),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _navigateToDetailedResults(context),
                          icon: const Icon(Icons.analytics),
                          label: const Text('View Details'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _saveLoan(context),
                          icon: const Icon(Icons.save),
                          label: const Text('Save Loan'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Additional action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _duplicateLoan(context),
                          icon: const Icon(Icons.copy),
                          label: const Text('Duplicate'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _newLoan(context),
                          icon: const Icon(Icons.add),
                          label: const Text('New Loan'),
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: 32),
              ],
            ),
          );
        },
      ),
    );
  }

  void _navigateToDetailedResults(BuildContext context) {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const ResultsScreen()));
  }

  Future<void> _saveLoan(BuildContext context) async {
    final loanProvider = context.read<LoanProvider>();
    final success = await loanProvider.saveCurrentLoan();

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success ? 'Loan saved successfully!' : 'Failed to save loan',
          ),
          backgroundColor: success
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  void _duplicateLoan(BuildContext context) {
    context.read<LoanProvider>().duplicateCurrentLoan();
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Loan duplicated')));
  }

  void _newLoan(BuildContext context) {
    context.read<LoanProvider>().createNewLoan();
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('New loan created')));
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('How to Use'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '1. Select your loan type\n'
                '2. Enter the loan amount\n'
                '3. Set the annual interest rate\n'
                '4. Choose the loan term\n'
                '5. Add down payment if applicable\n'
                '6. View calculated results\n'
                '7. Save or export your calculation',
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 16),
              Text('Tips:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text(
                '• Use the detailed view for amortization schedule\n'
                '• Save multiple loan scenarios for comparison\n'
                '• Export results as PDF for sharing',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}
