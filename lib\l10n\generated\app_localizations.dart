import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'Bank Loan Calculator'**
  String get appTitle;

  /// Calculator tab label
  ///
  /// In en, this message translates to:
  /// **'Calculator'**
  String get calculator;

  /// Saved loans tab label
  ///
  /// In en, this message translates to:
  /// **'Saved Loans'**
  String get savedLoans;

  /// Settings tab label
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// Loan calculator screen title
  ///
  /// In en, this message translates to:
  /// **'Loan Calculator'**
  String get loanCalculator;

  /// Loan details section title
  ///
  /// In en, this message translates to:
  /// **'Loan Details'**
  String get loanDetails;

  /// Loan name field label
  ///
  /// In en, this message translates to:
  /// **'Loan Name'**
  String get loanName;

  /// Loan name field hint
  ///
  /// In en, this message translates to:
  /// **'Enter a name for this loan'**
  String get enterLoanName;

  /// Loan name validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter a loan name'**
  String get pleaseEnterLoanName;

  /// Loan type field label
  ///
  /// In en, this message translates to:
  /// **'Loan Type'**
  String get loanType;

  /// Currency field label
  ///
  /// In en, this message translates to:
  /// **'Currency'**
  String get currency;

  /// Loan amount field label
  ///
  /// In en, this message translates to:
  /// **'Loan Amount'**
  String get loanAmount;

  /// Loan amount field hint
  ///
  /// In en, this message translates to:
  /// **'Enter loan amount'**
  String get enterLoanAmount;

  /// Loan amount validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter loan amount'**
  String get pleaseEnterLoanAmount;

  /// Loan amount validation message for invalid values
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid amount'**
  String get pleaseEnterValidAmount;

  /// Interest rate field label
  ///
  /// In en, this message translates to:
  /// **'Annual Interest Rate'**
  String get annualInterestRate;

  /// Interest rate field hint
  ///
  /// In en, this message translates to:
  /// **'Enter interest rate'**
  String get enterInterestRate;

  /// Interest rate validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter interest rate'**
  String get pleaseEnterInterestRate;

  /// Interest rate validation message for invalid values
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid rate (0-100%)'**
  String get pleaseEnterValidRate;

  /// Loan term field label
  ///
  /// In en, this message translates to:
  /// **'Loan Term'**
  String get loanTerm;

  /// Loan term field hint
  ///
  /// In en, this message translates to:
  /// **'Enter loan term'**
  String get enterLoanTerm;

  /// Loan term validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter loan term'**
  String get pleaseEnterLoanTerm;

  /// Loan term validation message for invalid values
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid term'**
  String get pleaseEnterValidTerm;

  /// Years label
  ///
  /// In en, this message translates to:
  /// **'Years'**
  String get years;

  /// Months label
  ///
  /// In en, this message translates to:
  /// **'Months'**
  String get months;

  /// Down payment field label
  ///
  /// In en, this message translates to:
  /// **'Down Payment (Optional)'**
  String get downPayment;

  /// Down payment field hint
  ///
  /// In en, this message translates to:
  /// **'Enter down payment'**
  String get enterDownPayment;

  /// Down payment validation message
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid down payment'**
  String get pleaseEnterValidDownPayment;

  /// Down payment validation message for amount too high
  ///
  /// In en, this message translates to:
  /// **'Down payment must be less than loan amount'**
  String get downPaymentMustBeLess;

  /// Results section title
  ///
  /// In en, this message translates to:
  /// **'Calculation Results'**
  String get calculationResults;

  /// Monthly payment label
  ///
  /// In en, this message translates to:
  /// **'Monthly Payment'**
  String get monthlyPayment;

  /// Principal amount label
  ///
  /// In en, this message translates to:
  /// **'Principal Amount'**
  String get principalAmount;

  /// Total interest label
  ///
  /// In en, this message translates to:
  /// **'Total Interest Paid'**
  String get totalInterestPaid;

  /// Total amount label
  ///
  /// In en, this message translates to:
  /// **'Total Amount Paid'**
  String get totalAmountPaid;

  /// Payment breakdown section title
  ///
  /// In en, this message translates to:
  /// **'Payment Breakdown'**
  String get paymentBreakdown;

  /// Principal label in breakdown
  ///
  /// In en, this message translates to:
  /// **'Principal'**
  String get principal;

  /// Interest label in breakdown
  ///
  /// In en, this message translates to:
  /// **'Interest'**
  String get interest;

  /// View details button
  ///
  /// In en, this message translates to:
  /// **'View Details'**
  String get viewDetails;

  /// Save loan button
  ///
  /// In en, this message translates to:
  /// **'Save Loan'**
  String get saveLoan;

  /// Duplicate button
  ///
  /// In en, this message translates to:
  /// **'Duplicate'**
  String get duplicate;

  /// New loan button
  ///
  /// In en, this message translates to:
  /// **'New Loan'**
  String get newLoan;

  /// Success message for saving loan
  ///
  /// In en, this message translates to:
  /// **'Loan saved successfully!'**
  String get loanSavedSuccessfully;

  /// Error message for saving loan
  ///
  /// In en, this message translates to:
  /// **'Failed to save loan'**
  String get failedToSaveLoan;

  /// Success message for duplicating loan
  ///
  /// In en, this message translates to:
  /// **'Loan duplicated'**
  String get loanDuplicated;

  /// Success message for creating new loan
  ///
  /// In en, this message translates to:
  /// **'New loan created'**
  String get newLoanCreated;

  /// Personal loan type
  ///
  /// In en, this message translates to:
  /// **'Personal Loan'**
  String get personalLoan;

  /// Mortgage loan type
  ///
  /// In en, this message translates to:
  /// **'Mortgage'**
  String get mortgage;

  /// Auto loan type
  ///
  /// In en, this message translates to:
  /// **'Auto Loan'**
  String get autoLoan;

  /// Business loan type
  ///
  /// In en, this message translates to:
  /// **'Business Loan'**
  String get businessLoan;

  /// Empty state message for saved loans
  ///
  /// In en, this message translates to:
  /// **'No Saved Loans'**
  String get noSavedLoans;

  /// Empty state description for saved loans
  ///
  /// In en, this message translates to:
  /// **'Calculate a loan and save it to see it here'**
  String get calculateLoanAndSave;

  /// Compare loans button
  ///
  /// In en, this message translates to:
  /// **'Compare Loans'**
  String get compareLoans;

  /// Clear all button
  ///
  /// In en, this message translates to:
  /// **'Clear All'**
  String get clearAll;

  /// Export all button
  ///
  /// In en, this message translates to:
  /// **'Export All'**
  String get exportAll;

  /// Delete button
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// Delete loan dialog title
  ///
  /// In en, this message translates to:
  /// **'Delete Loan'**
  String get deleteLoan;

  /// Delete loan confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this loan?'**
  String get areYouSureDeleteLoan;

  /// Delete multiple loans confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete {count} loans?'**
  String areYouSureDeleteLoans(int count);

  /// Cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Settings section title
  ///
  /// In en, this message translates to:
  /// **'Currency & Display'**
  String get currencyAndDisplay;

  /// Default currency setting
  ///
  /// In en, this message translates to:
  /// **'Default Currency'**
  String get defaultCurrency;

  /// Decimal places setting
  ///
  /// In en, this message translates to:
  /// **'Decimal Places'**
  String get decimalPlaces;

  /// Display settings section
  ///
  /// In en, this message translates to:
  /// **'Display'**
  String get display;

  /// Dark mode setting
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// Dark mode description
  ///
  /// In en, this message translates to:
  /// **'Use dark theme'**
  String get useDarkTheme;

  /// Detailed results setting
  ///
  /// In en, this message translates to:
  /// **'Detailed Results'**
  String get detailedResults;

  /// Detailed results description
  ///
  /// In en, this message translates to:
  /// **'Show detailed calculation results'**
  String get showDetailedResults;

  /// Calculations settings section
  ///
  /// In en, this message translates to:
  /// **'Calculations'**
  String get calculations;

  /// Auto-save setting
  ///
  /// In en, this message translates to:
  /// **'Auto-save Calculations'**
  String get autoSaveCalculations;

  /// Auto-save description
  ///
  /// In en, this message translates to:
  /// **'Automatically save loan calculations'**
  String get automaticallySave;

  /// Data management section
  ///
  /// In en, this message translates to:
  /// **'Data Management'**
  String get dataManagement;

  /// Export data option
  ///
  /// In en, this message translates to:
  /// **'Export Data'**
  String get exportData;

  /// Export data description
  ///
  /// In en, this message translates to:
  /// **'Export all saved loans'**
  String get exportAllSavedLoans;

  /// Import data option
  ///
  /// In en, this message translates to:
  /// **'Import Data'**
  String get importData;

  /// Import data description
  ///
  /// In en, this message translates to:
  /// **'Import saved loans'**
  String get importSavedLoans;

  /// Clear all data option
  ///
  /// In en, this message translates to:
  /// **'Clear All Data'**
  String get clearAllData;

  /// Clear all data description
  ///
  /// In en, this message translates to:
  /// **'Delete all saved loans and settings'**
  String get deleteAllSavedLoans;

  /// About section
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// App version label
  ///
  /// In en, this message translates to:
  /// **'App Version'**
  String get appVersion;

  /// Help and support option
  ///
  /// In en, this message translates to:
  /// **'Help & Support'**
  String get helpAndSupport;

  /// Privacy policy option
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// Reset settings button
  ///
  /// In en, this message translates to:
  /// **'Reset to Defaults'**
  String get resetToDefaults;

  /// Language setting
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Language selection dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// English language option
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// Arabic language option
  ///
  /// In en, this message translates to:
  /// **'العربية'**
  String get arabic;

  /// Summary tab label
  ///
  /// In en, this message translates to:
  /// **'Summary'**
  String get summary;

  /// Schedule tab label
  ///
  /// In en, this message translates to:
  /// **'Schedule'**
  String get schedule;

  /// Quick statistics section title
  ///
  /// In en, this message translates to:
  /// **'Quick Statistics'**
  String get quickStatistics;

  /// First payment label
  ///
  /// In en, this message translates to:
  /// **'First Payment'**
  String get firstPayment;

  /// Final payment label
  ///
  /// In en, this message translates to:
  /// **'Final Payment'**
  String get finalPayment;

  /// Interest rate label
  ///
  /// In en, this message translates to:
  /// **'Interest Rate'**
  String get interestRate;

  /// Total payments label
  ///
  /// In en, this message translates to:
  /// **'Total Payments'**
  String get totalPayments;

  /// Yearly summary section title
  ///
  /// In en, this message translates to:
  /// **'Yearly Summary'**
  String get yearlySummary;

  /// Share results button
  ///
  /// In en, this message translates to:
  /// **'Share Results'**
  String get shareResults;

  /// Export as PDF button
  ///
  /// In en, this message translates to:
  /// **'Export as PDF'**
  String get exportAsPdf;

  /// Monthly view option
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get monthly;

  /// Yearly view option
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get yearly;

  /// All years filter option
  ///
  /// In en, this message translates to:
  /// **'All Years'**
  String get allYears;

  /// Payment number column header
  ///
  /// In en, this message translates to:
  /// **'Payment #'**
  String get paymentNumber;

  /// Date column header
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// Payment column header
  ///
  /// In en, this message translates to:
  /// **'Payment'**
  String get payment;

  /// Balance column header
  ///
  /// In en, this message translates to:
  /// **'Balance'**
  String get balance;

  /// Payments column header
  ///
  /// In en, this message translates to:
  /// **'Payments'**
  String get payments;

  /// Total paid column header
  ///
  /// In en, this message translates to:
  /// **'Total Paid'**
  String get totalPaid;

  /// End balance column header
  ///
  /// In en, this message translates to:
  /// **'End Balance'**
  String get endBalance;

  /// Loan comparison screen title
  ///
  /// In en, this message translates to:
  /// **'Loan Comparison'**
  String get loanComparison;

  /// Quick comparison section title
  ///
  /// In en, this message translates to:
  /// **'Quick Comparison'**
  String get quickComparison;

  /// Difference label in comparison
  ///
  /// In en, this message translates to:
  /// **'Difference'**
  String get difference;

  /// Detailed comparison section title
  ///
  /// In en, this message translates to:
  /// **'Detailed Comparison'**
  String get detailedComparison;

  /// Recommendation section title
  ///
  /// In en, this message translates to:
  /// **'Recommendation'**
  String get recommendation;

  /// Recommendation when loans are similar
  ///
  /// In en, this message translates to:
  /// **'Both loans are very similar'**
  String get bothLoansAreSimilar;

  /// Recommendation when loan A is better
  ///
  /// In en, this message translates to:
  /// **'Loan A is better overall'**
  String get loanAIsBetter;

  /// Recommendation when loan B is better
  ///
  /// In en, this message translates to:
  /// **'Loan B is better overall'**
  String get loanBIsBetter;

  /// Note label
  ///
  /// In en, this message translates to:
  /// **'Note'**
  String get note;

  /// Recommendation disclaimer note
  ///
  /// In en, this message translates to:
  /// **'This recommendation is based purely on financial calculations. Consider other factors such as lender reputation, customer service, and loan terms.'**
  String get recommendationNote;

  /// Created date label
  ///
  /// In en, this message translates to:
  /// **'Created'**
  String get created;

  /// Amount label
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// Rate label
  ///
  /// In en, this message translates to:
  /// **'Rate'**
  String get rate;

  /// Term label
  ///
  /// In en, this message translates to:
  /// **'Term'**
  String get term;

  /// Per month suffix
  ///
  /// In en, this message translates to:
  /// **'per month'**
  String get perMonth;

  /// Select currency dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Currency'**
  String get selectCurrency;

  /// Close button
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// Got it button
  ///
  /// In en, this message translates to:
  /// **'Got it'**
  String get gotIt;

  /// How to use dialog title
  ///
  /// In en, this message translates to:
  /// **'How to Use'**
  String get howToUse;

  /// How to use content
  ///
  /// In en, this message translates to:
  /// **'1. Select your loan type\n2. Enter the loan amount\n3. Set the annual interest rate\n4. Choose the loan term\n5. Add down payment if applicable\n6. View calculated results\n7. Save or export your calculation'**
  String get howToUseContent;

  /// Tips section title
  ///
  /// In en, this message translates to:
  /// **'Tips'**
  String get tips;

  /// Tips content
  ///
  /// In en, this message translates to:
  /// **'• Use the detailed view for amortization schedule\n• Save multiple loan scenarios for comparison\n• Export results as PDF for sharing'**
  String get tipsContent;

  /// Help button
  ///
  /// In en, this message translates to:
  /// **'Help'**
  String get help;

  /// App name in help
  ///
  /// In en, this message translates to:
  /// **'Bank Loan Calculator'**
  String get bankLoanCalculator;

  /// Help content description
  ///
  /// In en, this message translates to:
  /// **'This app helps you calculate loan payments, compare different loan options, and understand the total cost of borrowing.'**
  String get helpContent;

  /// Features section title
  ///
  /// In en, this message translates to:
  /// **'Features'**
  String get features;

  /// Features list content
  ///
  /// In en, this message translates to:
  /// **'• Calculate monthly payments\n• View amortization schedules\n• Compare multiple loans\n• Save and manage loan calculations\n• Export results as PDF'**
  String get featuresContent;

  /// Support contact information
  ///
  /// In en, this message translates to:
  /// **'For support, please contact <NAME_EMAIL>'**
  String get support;

  /// Privacy policy content
  ///
  /// In en, this message translates to:
  /// **'This app stores loan calculation data locally on your device. No personal or financial information is transmitted to external servers. All calculations are performed locally and your data remains private.'**
  String get privacyPolicyContent;

  /// Reset settings dialog title
  ///
  /// In en, this message translates to:
  /// **'Reset Settings'**
  String get resetSettings;

  /// Reset settings confirmation message
  ///
  /// In en, this message translates to:
  /// **'Reset all settings to their default values?'**
  String get resetSettingsConfirm;

  /// Reset button
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get reset;

  /// Settings reset success message
  ///
  /// In en, this message translates to:
  /// **'Settings reset to defaults'**
  String get settingsResetToDefaults;

  /// Clear all loans dialog title
  ///
  /// In en, this message translates to:
  /// **'Clear All Loans'**
  String get clearAllLoans;

  /// Clear all loans confirmation message
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete all saved loans? This action cannot be undone.'**
  String get clearAllLoansConfirm;

  /// All data cleared success message
  ///
  /// In en, this message translates to:
  /// **'All data cleared successfully'**
  String get allDataCleared;

  /// Export feature coming soon message
  ///
  /// In en, this message translates to:
  /// **'Export feature coming soon'**
  String get exportFeatureComingSoon;

  /// Import feature coming soon message
  ///
  /// In en, this message translates to:
  /// **'Import feature coming soon'**
  String get importFeatureComingSoon;

  /// Share comparison coming soon message
  ///
  /// In en, this message translates to:
  /// **'Share comparison feature coming soon'**
  String get shareComparisonComingSoon;

  /// Export all coming soon message
  ///
  /// In en, this message translates to:
  /// **'Export all feature coming soon'**
  String get exportAllFeatureComingSoon;

  /// PDF export success message
  ///
  /// In en, this message translates to:
  /// **'PDF exported successfully'**
  String get pdfExportedSuccessfully;

  /// PDF export error message
  ///
  /// In en, this message translates to:
  /// **'Error exporting PDF'**
  String get errorExportingPdf;

  /// Share results error message
  ///
  /// In en, this message translates to:
  /// **'Error sharing results'**
  String get errorSharingResults;

  /// Compare loans error message
  ///
  /// In en, this message translates to:
  /// **'Error comparing loans'**
  String get errorComparingLoans;

  /// Select loans to compare message
  ///
  /// In en, this message translates to:
  /// **'Select 2 loans to compare'**
  String get select2LoansToCompare;

  /// Selected items count suffix
  ///
  /// In en, this message translates to:
  /// **'selected'**
  String get selected;

  /// Compare button
  ///
  /// In en, this message translates to:
  /// **'Compare'**
  String get compare;

  /// Delete selected button
  ///
  /// In en, this message translates to:
  /// **'Delete Selected'**
  String get deleteSelected;

  /// Loan A label
  ///
  /// In en, this message translates to:
  /// **'Loan A'**
  String get loanA;

  /// Loan B label
  ///
  /// In en, this message translates to:
  /// **'Loan B'**
  String get loanB;

  /// Copy button
  ///
  /// In en, this message translates to:
  /// **'Copy'**
  String get copy;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
