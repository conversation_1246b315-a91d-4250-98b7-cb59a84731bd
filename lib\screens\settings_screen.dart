import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../providers/loan_provider.dart';
import '../models/currency.dart';
import '../utils/currency_utils.dart';

/// Screen for app settings and preferences
class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Language & Currency Settings
              _buildSectionHeader(context, 'Language & Currency'),
              Card(
                child: Column(
                  children: [
                    const ListTile(
                      leading: Icon(Icons.language),
                      title: Text('Language'),
                      subtitle: Text('English'),
                      trailing: Icon(Icons.arrow_forward_ios),
                      enabled: false, // Disabled since we removed localization
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.attach_money),
                      title: const Text('Default Currency'),
                      subtitle: Text(
                        '${settingsProvider.defaultCurrency.code} (${settingsProvider.defaultCurrency.symbol})',
                      ),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () =>
                          _showCurrencyPicker(context, settingsProvider),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.format_list_numbered),
                      title: const Text('Decimal Places'),
                      subtitle: Text(
                        '${settingsProvider.decimalPlaces} places',
                      ),
                      trailing: DropdownButton<int>(
                        value: settingsProvider.decimalPlaces,
                        items: settingsProvider.availableDecimalPlaces
                            .map(
                              (places) => DropdownMenuItem(
                                value: places,
                                child: Text(places.toString()),
                              ),
                            )
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            settingsProvider.setDecimalPlaces(value);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Display Settings
              _buildSectionHeader(context, 'Display'),
              Card(
                child: Column(
                  children: [
                    SwitchListTile(
                      secondary: const Icon(Icons.dark_mode),
                      title: const Text('Dark Mode'),
                      subtitle: const Text('Use dark theme'),
                      value: settingsProvider.darkMode,
                      onChanged: (value) => settingsProvider.setDarkMode(value),
                    ),
                    const Divider(height: 1),
                    SwitchListTile(
                      secondary: const Icon(Icons.analytics),
                      title: const Text('Detailed Results'),
                      subtitle: const Text('Show detailed calculation results'),
                      value: settingsProvider.showDetailedResults,
                      onChanged: (value) =>
                          settingsProvider.setShowDetailedResults(value),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Calculation Settings
              _buildSectionHeader(context, 'Calculations'),
              Card(
                child: Column(
                  children: [
                    SwitchListTile(
                      secondary: const Icon(Icons.auto_awesome),
                      title: const Text('Auto-save Calculations'),
                      subtitle: const Text(
                        'Automatically save loan calculations',
                      ),
                      value: settingsProvider.autoSaveCalculations,
                      onChanged: (value) =>
                          settingsProvider.setAutoSaveCalculations(value),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Data Management
              _buildSectionHeader(context, 'Data Management'),
              Card(
                child: Column(
                  children: [
                    ListTile(
                      leading: const Icon(Icons.file_download),
                      title: const Text('Export Data'),
                      subtitle: const Text('Export all saved loans'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _exportData(context),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.file_upload),
                      title: const Text('Import Data'),
                      subtitle: const Text('Import saved loans'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _importData(context),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: Icon(
                        Icons.delete_forever,
                        color: Theme.of(context).colorScheme.error,
                      ),
                      title: Text(
                        'Clear All Data',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.error,
                        ),
                      ),
                      subtitle: const Text(
                        'Delete all saved loans and settings',
                      ),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _clearAllData(context),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // About
              _buildSectionHeader(context, 'About'),
              Card(
                child: Column(
                  children: [
                    ListTile(
                      leading: const Icon(Icons.info),
                      title: const Text('App Version'),
                      subtitle: const Text('1.0.0'),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.help),
                      title: const Text('Help & Support'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _showHelp(context),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      leading: const Icon(Icons.privacy_tip),
                      title: const Text('Privacy Policy'),
                      trailing: const Icon(Icons.arrow_forward_ios),
                      onTap: () => _showPrivacyPolicy(context),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Reset Settings
              Center(
                child: OutlinedButton.icon(
                  onPressed: () => _resetSettings(context, settingsProvider),
                  icon: const Icon(Icons.restore),
                  label: const Text('Reset to Defaults'),
                ),
              ),

              const SizedBox(height: 32),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  void _showCurrencyPicker(
    BuildContext context,
    SettingsProvider settingsProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Currency'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: Currency.values.length,
            itemBuilder: (context, index) {
              final currency = Currency.values[index];
              final isSelected = currency == settingsProvider.defaultCurrency;

              return ListTile(
                leading: Text(
                  CurrencyUtils.getCurrencyFlag(currency),
                  style: const TextStyle(fontSize: 20),
                ),
                title: Text(currency.name),
                subtitle: Text('${currency.code} (${currency.symbol})'),
                trailing: isSelected ? const Icon(Icons.check) : null,
                selected: isSelected,
                onTap: () {
                  settingsProvider.setDefaultCurrency(currency);
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _exportData(BuildContext context) {
    // TODO: Implement export functionality
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Export feature coming soon')));
  }

  void _importData(BuildContext context) {
    // TODO: Implement import functionality
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('Import feature coming soon')));
  }

  Future<void> _clearAllData(BuildContext context) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will delete all saved loans and reset settings to defaults. '
          'This action cannot be undone. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Clear All'),
          ),
        ],
      ),
    );

    if (confirmed == true && context.mounted) {
      await context.read<LoanProvider>().clearAllSavedLoans();
      await context.read<SettingsProvider>().resetToDefaults();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('All data cleared successfully')),
        );
      }
    }
  }

  Future<void> _resetSettings(
    BuildContext context,
    SettingsProvider settingsProvider,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Reset all settings to their default values?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Reset'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await settingsProvider.resetToDefaults();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Settings reset to defaults')),
        );
      }
    }
  }

  void _showHelp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & Support'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Bank Loan Calculator',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              SizedBox(height: 8),
              Text(
                'This app helps you calculate loan payments, compare different loan options, and understand the total cost of borrowing.',
              ),
              SizedBox(height: 16),
              Text('Features:', style: TextStyle(fontWeight: FontWeight.bold)),
              Text(
                '• Calculate monthly payments\n'
                '• View amortization schedules\n'
                '• Compare multiple loans\n'
                '• Save and manage loan calculations\n'
                '• Export results as PDF',
              ),
              SizedBox(height: 16),
              Text('For support, please contact <NAME_EMAIL>'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Privacy Policy'),
        content: const SingleChildScrollView(
          child: Text(
            'This app stores loan calculation data locally on your device. '
            'No personal or financial information is transmitted to external servers. '
            'All calculations are performed locally and your data remains private.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
