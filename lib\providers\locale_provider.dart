import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Provider for managing app locale and language settings
class LocaleProvider extends ChangeNotifier {
  static const String _localeKey = 'app_locale';
  
  Locale _locale = const Locale('en'); // Default to English
  bool _isLoading = false;

  // Getters
  Locale get locale => _locale;
  bool get isLoading => _isLoading;
  bool get isArabic => _locale.languageCode == 'ar';
  bool get isEnglish => _locale.languageCode == 'en';

  /// Supported locales
  static const List<Locale> supportedLocales = [
    Locale('en'), // English
    Locale('ar'), // Arabic
  ];

  /// Load saved locale from storage
  Future<void> loadLocale() async {
    _setLoading(true);
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedLocale = prefs.getString(_localeKey);
      
      if (savedLocale != null) {
        // Validate that the saved locale is supported
        final locale = Locale(savedLocale);
        if (supportedLocales.contains(locale)) {
          _locale = locale;
        }
      } else {
        // If no saved locale, try to use system locale if supported
        _setSystemLocaleIfSupported();
      }
    } catch (e) {
      debugPrint('Error loading locale: $e');
      // Fall back to default locale
      _locale = const Locale('en');
    } finally {
      _setLoading(false);
    }
  }

  /// Set the app locale
  Future<void> setLocale(Locale locale) async {
    if (!supportedLocales.contains(locale)) {
      debugPrint('Unsupported locale: $locale');
      return;
    }

    _locale = locale;
    notifyListeners();
    
    // Save to storage
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_localeKey, locale.languageCode);
    } catch (e) {
      debugPrint('Error saving locale: $e');
    }
  }

  /// Toggle between English and Arabic
  Future<void> toggleLanguage() async {
    final newLocale = _locale.languageCode == 'en' 
        ? const Locale('ar') 
        : const Locale('en');
    await setLocale(newLocale);
  }

  /// Set to English
  Future<void> setEnglish() async {
    await setLocale(const Locale('en'));
  }

  /// Set to Arabic
  Future<void> setArabic() async {
    await setLocale(const Locale('ar'));
  }

  /// Try to set system locale if it's supported
  void _setSystemLocaleIfSupported() {
    try {
      final systemLocales = WidgetsBinding.instance.platformDispatcher.locales;
      
      for (final systemLocale in systemLocales) {
        // Check if we support this language
        final supportedLocale = supportedLocales.firstWhere(
          (locale) => locale.languageCode == systemLocale.languageCode,
          orElse: () => const Locale('en'),
        );
        
        if (supportedLocale.languageCode != 'en' || 
            systemLocale.languageCode == 'en') {
          _locale = supportedLocale;
          break;
        }
      }
    } catch (e) {
      debugPrint('Error getting system locale: $e');
      _locale = const Locale('en');
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Get locale display name
  String getLocaleDisplayName(Locale locale) {
    switch (locale.languageCode) {
      case 'ar':
        return 'العربية';
      case 'en':
        return 'English';
      default:
        return locale.languageCode.toUpperCase();
    }
  }

  /// Get current locale display name
  String get currentLocaleDisplayName => getLocaleDisplayName(_locale);

  /// Check if RTL (Right-to-Left) should be used
  bool get isRTL => _locale.languageCode == 'ar';

  /// Get text direction based on current locale
  TextDirection get textDirection => isRTL ? TextDirection.rtl : TextDirection.ltr;

  /// Reset to system locale or default
  Future<void> resetToSystemLocale() async {
    _setSystemLocaleIfSupported();
    notifyListeners();
    
    // Save to storage
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_localeKey, _locale.languageCode);
    } catch (e) {
      debugPrint('Error saving locale: $e');
    }
  }

  /// Clear saved locale (will use system locale on next load)
  Future<void> clearSavedLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_localeKey);
      _setSystemLocaleIfSupported();
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing saved locale: $e');
    }
  }

  /// Get locale from language code
  static Locale? getLocaleFromCode(String languageCode) {
    try {
      return supportedLocales.firstWhere(
        (locale) => locale.languageCode == languageCode,
      );
    } catch (e) {
      return null;
    }
  }

  /// Check if a locale is supported
  static bool isLocaleSupported(Locale locale) {
    return supportedLocales.any(
      (supportedLocale) => supportedLocale.languageCode == locale.languageCode,
    );
  }
}
