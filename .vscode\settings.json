{"dart.flutterSdkPath": null, "dart.lineLength": 80, "dart.insertArgumentPlaceholders": false, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.closingLabels": true, "dart.openDevTools": "flutter", "dart.debugExternalPackageLibraries": false, "dart.debugSdkLibraries": false, "dart.analysisExcludedFolders": ["build", ".dart_tool"], "editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": false, "files.associations": {"*.dart": "dart"}, "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/.dart_tool": true, "**/build": true, "**/*.g.dart": true, "**/*.freezed.dart": true}, "search.exclude": {"**/.dart_tool": true, "**/build": true, "**/*.g.dart": true, "**/*.freezed.dart": true}}