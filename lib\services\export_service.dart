import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../providers/loan_provider.dart';
import '../utils/formatters.dart';

/// Service for exporting loan calculations and sharing results
class ExportService {
  /// Exports the current loan calculation to PDF
  static Future<void> exportToPdf(BuildContext context) async {
    final loanProvider = context.read<LoanProvider>();
    final loan = loanProvider.currentLoan;
    final schedule = loanProvider.currentSchedule;

    if (!loan.isValid || schedule == null) {
      throw Exception('No valid loan calculation to export');
    }

    // Create PDF document
    final pdf = pw.Document();

    // Add pages
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        build: (pw.Context context) {
          return [
            _buildPdfHeader(loan),
            pw.SizedBox(height: 20),
            _buildPdfSummary(loan),
            pw.SizedBox(height: 20),
            _buildPdfAmortizationTable(loan, schedule),
          ];
        },
      ),
    );

    // Save and share PDF
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/loan_calculation_${loan.name.replaceAll(' ', '_')}.pdf');
    await file.writeAsBytes(await pdf.save());

    await Share.shareXFiles(
      [XFile(file.path)],
      text: 'Loan calculation for ${loan.name}',
    );
  }

  /// Shares loan results as text
  static Future<void> shareResults(BuildContext context) async {
    final loanProvider = context.read<LoanProvider>();
    final loan = loanProvider.currentLoan;

    if (!loan.isValid) {
      throw Exception('No valid loan calculation to share');
    }

    final text = _generateShareText(loan);
    await Share.share(text, subject: 'Loan Calculation: ${loan.name}');
  }

  /// Generates text summary for sharing
  static String _generateShareText(loan) {
    final buffer = StringBuffer();
    
    buffer.writeln('🏦 Loan Calculation: ${loan.name}');
    buffer.writeln('');
    buffer.writeln('📊 Loan Details:');
    buffer.writeln('• Type: ${loan.loanType.displayName}');
    buffer.writeln('• Amount: ${Formatters.formatCurrency(loan.loanAmount, loan.currency)}');
    buffer.writeln('• Interest Rate: ${Formatters.formatPercentage(loan.annualInterestRate)}');
    buffer.writeln('• Term: ${Formatters.formatLoanTerm(loan.termInMonths)}');
    
    if (loan.downPayment > 0) {
      buffer.writeln('• Down Payment: ${Formatters.formatCurrency(loan.downPayment, loan.currency)}');
    }
    
    buffer.writeln('');
    buffer.writeln('💰 Results:');
    buffer.writeln('• Monthly Payment: ${Formatters.formatCurrency(loan.monthlyPayment, loan.currency)}');
    buffer.writeln('• Total Interest: ${Formatters.formatCurrency(loan.totalInterestPaid, loan.currency)}');
    buffer.writeln('• Total Amount: ${Formatters.formatCurrency(loan.totalAmountPaid, loan.currency)}');
    
    buffer.writeln('');
    buffer.writeln('Generated by Bank Loan Calculator');
    
    return buffer.toString();
  }

  /// Builds PDF header
  static pw.Widget _buildPdfHeader(loan) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Loan Calculation Report',
          style: pw.TextStyle(
            fontSize: 24,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 8),
        pw.Text(
          loan.name,
          style: pw.TextStyle(
            fontSize: 18,
            color: PdfColors.grey700,
          ),
        ),
        pw.SizedBox(height: 4),
        pw.Text(
          'Generated on ${Formatters.formatDate(DateTime.now())}',
          style: pw.TextStyle(
            fontSize: 12,
            color: PdfColors.grey600,
          ),
        ),
        pw.Divider(),
      ],
    );
  }

  /// Builds PDF summary section
  static pw.Widget _buildPdfSummary(loan) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Loan Summary',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 12),
        
        // Loan details table
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          children: [
            _buildPdfTableRow('Loan Type', loan.loanType.displayName),
            _buildPdfTableRow('Loan Amount', Formatters.formatCurrency(loan.loanAmount, loan.currency)),
            _buildPdfTableRow('Interest Rate', Formatters.formatPercentage(loan.annualInterestRate)),
            _buildPdfTableRow('Loan Term', Formatters.formatLoanTerm(loan.termInMonths)),
            if (loan.downPayment > 0)
              _buildPdfTableRow('Down Payment', Formatters.formatCurrency(loan.downPayment, loan.currency)),
            _buildPdfTableRow('Principal Amount', Formatters.formatCurrency(loan.principalAmount, loan.currency)),
          ],
        ),
        
        pw.SizedBox(height: 16),
        
        // Results table
        pw.Text(
          'Calculation Results',
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 8),
        
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          children: [
            _buildPdfTableRow('Monthly Payment', Formatters.formatCurrency(loan.monthlyPayment, loan.currency), isHighlight: true),
            _buildPdfTableRow('Total Interest Paid', Formatters.formatCurrency(loan.totalInterestPaid, loan.currency)),
            _buildPdfTableRow('Total Amount Paid', Formatters.formatCurrency(loan.totalAmountPaid, loan.currency)),
          ],
        ),
      ],
    );
  }

  /// Builds PDF table row
  static pw.TableRow _buildPdfTableRow(String label, String value, {bool isHighlight = false}) {
    return pw.TableRow(
      decoration: isHighlight 
          ? const pw.BoxDecoration(color: PdfColors.grey100)
          : null,
      children: [
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            label,
            style: pw.TextStyle(
              fontWeight: isHighlight ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
        ),
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            value,
            style: pw.TextStyle(
              fontWeight: isHighlight ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
        ),
      ],
    );
  }

  /// Builds PDF amortization table (first 12 payments)
  static pw.Widget _buildPdfAmortizationTable(loan, schedule) {
    final payments = schedule.payments.take(12).toList();
    
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'Amortization Schedule (First 12 Payments)',
          style: pw.TextStyle(
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
          ),
        ),
        pw.SizedBox(height: 12),
        
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey300),
          columnWidths: {
            0: const pw.FixedColumnWidth(40),
            1: const pw.FixedColumnWidth(60),
            2: const pw.FixedColumnWidth(80),
            3: const pw.FixedColumnWidth(80),
            4: const pw.FixedColumnWidth(80),
            5: const pw.FixedColumnWidth(80),
          },
          children: [
            // Header
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildPdfTableCell('#', isHeader: true),
                _buildPdfTableCell('Date', isHeader: true),
                _buildPdfTableCell('Payment', isHeader: true),
                _buildPdfTableCell('Principal', isHeader: true),
                _buildPdfTableCell('Interest', isHeader: true),
                _buildPdfTableCell('Balance', isHeader: true),
              ],
            ),
            
            // Data rows
            ...payments.map((payment) {
              return pw.TableRow(
                children: [
                  _buildPdfTableCell(payment.paymentNumber.toString()),
                  _buildPdfTableCell(Formatters.formatMonthYear(payment.paymentDate)),
                  _buildPdfTableCell(Formatters.formatCurrency(payment.paymentAmount, loan.currency)),
                  _buildPdfTableCell(Formatters.formatCurrency(payment.principalAmount, loan.currency)),
                  _buildPdfTableCell(Formatters.formatCurrency(payment.interestAmount, loan.currency)),
                  _buildPdfTableCell(Formatters.formatCurrency(payment.remainingBalance, loan.currency)),
                ],
              );
            }).toList(),
          ],
        ),
        
        if (schedule.payments.length > 12) ...[
          pw.SizedBox(height: 8),
          pw.Text(
            '... and ${schedule.payments.length - 12} more payments',
            style: pw.TextStyle(
              fontSize: 10,
              fontStyle: pw.FontStyle.italic,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ],
    );
  }

  /// Builds PDF table cell
  static pw.Widget _buildPdfTableCell(String text, {bool isHeader = false}) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(4),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 10 : 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
      ),
    );
  }
}
