// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'loan_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoanModel _$LoanModelFromJson(Map<String, dynamic> json) => LoanModel(
  id: json['id'] as String,
  name: json['name'] as String,
  loanType: $enumDecode(_$LoanTypeEnumMap, json['loanType']),
  loanAmount: (json['loanAmount'] as num).toDouble(),
  annualInterestRate: (json['annualInterestRate'] as num).toDouble(),
  termInMonths: (json['termInMonths'] as num).toInt(),
  downPayment: (json['downPayment'] as num?)?.toDouble() ?? 0.0,
  currency:
      $enumDecodeNullable(_$CurrencyEnumMap, json['currency']) ?? Currency.usd,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: json['updatedAt'] == null
      ? null
      : DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$LoanModelToJson(LoanModel instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'loanType': _$LoanTypeEnumMap[instance.loanType]!,
  'loanAmount': instance.loanAmount,
  'annualInterestRate': instance.annualInterestRate,
  'termInMonths': instance.termInMonths,
  'downPayment': instance.downPayment,
  'currency': _$CurrencyEnumMap[instance.currency]!,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
};

const _$LoanTypeEnumMap = {
  LoanType.personal: 'personal',
  LoanType.mortgage: 'mortgage',
  LoanType.auto: 'auto',
  LoanType.business: 'business',
};

const _$CurrencyEnumMap = {
  Currency.usd: 'usd',
  Currency.eur: 'eur',
  Currency.gbp: 'gbp',
  Currency.cad: 'cad',
  Currency.aud: 'aud',
  Currency.jpy: 'jpy',
  Currency.inr: 'inr',
  Currency.cny: 'cny',
};
