import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/currency.dart';
import '../services/storage_service.dart';

/// Provider for managing app settings and preferences
class SettingsProvider extends ChangeNotifier {
  Currency _defaultCurrency = Currency.usd;
  bool _showDetailedResults = true;
  bool _enableNotifications = false;
  bool _darkMode = false;
  bool _autoSaveCalculations = true;
  int _decimalPlaces = 2;
  bool _isLoading = false;

  // Getters
  Currency get defaultCurrency => _defaultCurrency;
  bool get showDetailedResults => _showDetailedResults;
  bool get enableNotifications => _enableNotifications;
  bool get darkMode => _darkMode;
  bool get autoSaveCalculations => _autoSaveCalculations;
  int get decimalPlaces => _decimalPlaces;
  bool get isLoading => _isLoading;

  /// Returns the current theme mode
  ThemeMode get themeMode => _darkMode ? ThemeMode.dark : ThemeMode.light;

  /// Loads settings from storage
  Future<void> loadSettings() async {
    _setLoading(true);
    
    try {
      final settings = await StorageService.getSettings();
      
      // Parse currency
      final currencyCode = settings['defaultCurrency'] as String?;
      if (currencyCode != null) {
        for (final currency in Currency.values) {
          if (currency.code == currencyCode) {
            _defaultCurrency = currency;
            break;
          }
        }
      }
      
      _showDetailedResults = settings['showDetailedResults'] as bool? ?? true;
      _enableNotifications = settings['enableNotifications'] as bool? ?? false;
      _darkMode = settings['darkMode'] as bool? ?? false;
      _autoSaveCalculations = settings['autoSaveCalculations'] as bool? ?? true;
      _decimalPlaces = settings['decimalPlaces'] as int? ?? 2;
      
    } catch (e) {
      debugPrint('Error loading settings: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Saves current settings to storage
  Future<void> saveSettings() async {
    try {
      final settings = {
        'defaultCurrency': _defaultCurrency.code,
        'showDetailedResults': _showDetailedResults,
        'enableNotifications': _enableNotifications,
        'darkMode': _darkMode,
        'autoSaveCalculations': _autoSaveCalculations,
        'decimalPlaces': _decimalPlaces,
      };
      
      await StorageService.saveSettings(settings);
    } catch (e) {
      debugPrint('Error saving settings: $e');
    }
  }

  /// Sets the default currency
  Future<void> setDefaultCurrency(Currency currency) async {
    _defaultCurrency = currency;
    notifyListeners();
    await saveSettings();
    await StorageService.setDefaultCurrency(currency);
  }

  /// Toggles detailed results display
  Future<void> toggleDetailedResults() async {
    _showDetailedResults = !_showDetailedResults;
    notifyListeners();
    await saveSettings();
  }

  /// Sets detailed results display
  Future<void> setShowDetailedResults(bool value) async {
    _showDetailedResults = value;
    notifyListeners();
    await saveSettings();
  }

  /// Toggles notifications
  Future<void> toggleNotifications() async {
    _enableNotifications = !_enableNotifications;
    notifyListeners();
    await saveSettings();
  }

  /// Sets notifications enabled
  Future<void> setEnableNotifications(bool value) async {
    _enableNotifications = value;
    notifyListeners();
    await saveSettings();
  }

  /// Toggles dark mode
  Future<void> toggleDarkMode() async {
    _darkMode = !_darkMode;
    notifyListeners();
    await saveSettings();
  }

  /// Sets dark mode
  Future<void> setDarkMode(bool value) async {
    _darkMode = value;
    notifyListeners();
    await saveSettings();
  }

  /// Toggles auto-save calculations
  Future<void> toggleAutoSaveCalculations() async {
    _autoSaveCalculations = !_autoSaveCalculations;
    notifyListeners();
    await saveSettings();
  }

  /// Sets auto-save calculations
  Future<void> setAutoSaveCalculations(bool value) async {
    _autoSaveCalculations = value;
    notifyListeners();
    await saveSettings();
  }

  /// Sets decimal places for currency display
  Future<void> setDecimalPlaces(int places) async {
    if (places >= 0 && places <= 4) {
      _decimalPlaces = places;
      notifyListeners();
      await saveSettings();
    }
  }

  /// Resets all settings to defaults
  Future<void> resetToDefaults() async {
    _defaultCurrency = Currency.usd;
    _showDetailedResults = true;
    _enableNotifications = false;
    _darkMode = false;
    _autoSaveCalculations = true;
    _decimalPlaces = 2;
    
    notifyListeners();
    await saveSettings();
  }

  /// Sets loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Gets all available currencies
  List<Currency> get availableCurrencies => Currency.values;

  /// Gets available decimal place options
  List<int> get availableDecimalPlaces => [0, 1, 2, 3, 4];

  /// Exports settings as JSON
  Map<String, dynamic> exportSettings() {
    return {
      'defaultCurrency': _defaultCurrency.code,
      'showDetailedResults': _showDetailedResults,
      'enableNotifications': _enableNotifications,
      'darkMode': _darkMode,
      'autoSaveCalculations': _autoSaveCalculations,
      'decimalPlaces': _decimalPlaces,
      'exportDate': DateTime.now().toIso8601String(),
    };
  }

  /// Imports settings from JSON
  Future<bool> importSettings(Map<String, dynamic> settings) async {
    try {
      // Validate and import currency
      final currencyCode = settings['defaultCurrency'] as String?;
      if (currencyCode != null) {
        for (final currency in Currency.values) {
          if (currency.code == currencyCode) {
            _defaultCurrency = currency;
            break;
          }
        }
      }

      _showDetailedResults = settings['showDetailedResults'] as bool? ?? _showDetailedResults;
      _enableNotifications = settings['enableNotifications'] as bool? ?? _enableNotifications;
      _darkMode = settings['darkMode'] as bool? ?? _darkMode;
      _autoSaveCalculations = settings['autoSaveCalculations'] as bool? ?? _autoSaveCalculations;
      
      final decimalPlaces = settings['decimalPlaces'] as int? ?? _decimalPlaces;
      if (decimalPlaces >= 0 && decimalPlaces <= 4) {
        _decimalPlaces = decimalPlaces;
      }

      notifyListeners();
      await saveSettings();
      return true;
    } catch (e) {
      debugPrint('Error importing settings: $e');
      return false;
    }
  }
}
