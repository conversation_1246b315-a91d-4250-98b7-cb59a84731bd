// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_schedule.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentScheduleItem _$PaymentScheduleItemFromJson(Map<String, dynamic> json) =>
    PaymentScheduleItem(
      paymentNumber: (json['paymentNumber'] as num).toInt(),
      paymentAmount: (json['paymentAmount'] as num).toDouble(),
      principalAmount: (json['principalAmount'] as num).toDouble(),
      interestAmount: (json['interestAmount'] as num).toDouble(),
      remainingBalance: (json['remainingBalance'] as num).toDouble(),
      paymentDate: DateTime.parse(json['paymentDate'] as String),
    );

Map<String, dynamic> _$PaymentScheduleItemToJson(
  PaymentScheduleItem instance,
) => <String, dynamic>{
  'paymentNumber': instance.paymentNumber,
  'paymentAmount': instance.paymentAmount,
  'principalAmount': instance.principalAmount,
  'interestAmount': instance.interestAmount,
  'remainingBalance': instance.remainingBalance,
  'paymentDate': instance.paymentDate.toIso8601String(),
};

PaymentSchedule _$PaymentScheduleFromJson(Map<String, dynamic> json) =>
    PaymentSchedule(
      payments: (json['payments'] as List<dynamic>)
          .map((e) => PaymentScheduleItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalInterestPaid: (json['totalInterestPaid'] as num).toDouble(),
      totalAmountPaid: (json['totalAmountPaid'] as num).toDouble(),
      totalPayments: (json['totalPayments'] as num).toInt(),
    );

Map<String, dynamic> _$PaymentScheduleToJson(PaymentSchedule instance) =>
    <String, dynamic>{
      'payments': instance.payments,
      'totalInterestPaid': instance.totalInterestPaid,
      'totalAmountPaid': instance.totalAmountPaid,
      'totalPayments': instance.totalPayments,
    };
