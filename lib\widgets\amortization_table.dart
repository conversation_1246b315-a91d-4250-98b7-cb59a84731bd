import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/loan_provider.dart';
import '../utils/formatters.dart';

/// Widget that displays the amortization schedule in a table format
class AmortizationTable extends StatefulWidget {
  const AmortizationTable({super.key});

  @override
  State<AmortizationTable> createState() => _AmortizationTableState();
}

class _AmortizationTableState extends State<AmortizationTable> {
  bool _showMonthlyView = true;
  int? _selectedYear;

  @override
  Widget build(BuildContext context) {
    return Consumer<LoanProvider>(
      builder: (context, loanProvider, child) {
        final loan = loanProvider.currentLoan;
        final schedule = loanProvider.currentSchedule;

        if (schedule == null || schedule.payments.isEmpty) {
          return const Center(
            child: Text('No amortization schedule available'),
          );
        }

        final years = schedule.years;
        final payments = _selectedYear != null
            ? schedule.getPaymentsForYear(_selectedYear!)
            : schedule.payments;

        return Column(
          children: [
            // Controls
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // View toggle
                  SegmentedButton<bool>(
                    segments: const [
                      ButtonSegment(
                        value: true,
                        label: Text('Monthly'),
                        icon: Icon(Icons.calendar_view_month),
                      ),
                      ButtonSegment(
                        value: false,
                        label: Text('Yearly'),
                        icon: Icon(Icons.calendar_today),
                      ),
                    ],
                    selected: {_showMonthlyView},
                    onSelectionChanged: (selection) {
                      setState(() {
                        _showMonthlyView = selection.first;
                        if (!_showMonthlyView) {
                          _selectedYear = null;
                        }
                      });
                    },
                  ),

                  if (_showMonthlyView && years.length > 1) ...[
                    const SizedBox(height: 12),
                    // Year filter
                    Wrap(
                      spacing: 8,
                      children: [
                        FilterChip(
                          label: const Text('All Years'),
                          selected: _selectedYear == null,
                          onSelected: (selected) {
                            setState(() {
                              _selectedYear = selected ? null : _selectedYear;
                            });
                          },
                        ),
                        ...years.map((year) {
                          return FilterChip(
                            label: Text(year.toString()),
                            selected: _selectedYear == year,
                            onSelected: (selected) {
                              setState(() {
                                _selectedYear = selected ? year : null;
                              });
                            },
                          );
                        }).toList(),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            // Table
            Expanded(
              child: _showMonthlyView
                  ? _buildMonthlyTable(context, loan, payments)
                  : _buildYearlyTable(context, loan, schedule),
            ),
          ],
        );
      },
    );
  }

  Widget _buildMonthlyTable(BuildContext context, loan, payments) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columnSpacing: 16,
          horizontalMargin: 16,
          columns: const [
            DataColumn(label: Text('Payment #')),
            DataColumn(label: Text('Date')),
            DataColumn(label: Text('Payment')),
            DataColumn(label: Text('Principal')),
            DataColumn(label: Text('Interest')),
            DataColumn(label: Text('Balance')),
          ],
          rows: payments.map<DataRow>((payment) {
            return DataRow(
              cells: [
                DataCell(Text(payment.paymentNumber.toString())),
                DataCell(Text(Formatters.formatMonthYear(payment.paymentDate))),
                DataCell(
                  Text(
                    Formatters.formatCurrency(
                      payment.paymentAmount,
                      loan.currency,
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    Formatters.formatCurrency(
                      payment.principalAmount,
                      loan.currency,
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    Formatters.formatCurrency(
                      payment.interestAmount,
                      loan.currency,
                    ),
                  ),
                ),
                DataCell(
                  Text(
                    Formatters.formatCurrency(
                      payment.remainingBalance,
                      loan.currency,
                    ),
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildYearlyTable(BuildContext context, loan, schedule) {
    final years = schedule.years;

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: DataTable(
          columnSpacing: 16,
          horizontalMargin: 16,
          columns: const [
            DataColumn(label: Text('Year')),
            DataColumn(label: Text('Payments')),
            DataColumn(label: Text('Total Paid')),
            DataColumn(label: Text('Principal')),
            DataColumn(label: Text('Interest')),
            DataColumn(label: Text('End Balance')),
          ],
          rows: years.map<DataRow>((year) {
            final yearPayments = schedule.getPaymentsForYear(year);
            final yearInterest = schedule.getInterestForYear(year);
            final yearPrincipal = schedule.getPrincipalForYear(year);
            final totalPaid = yearInterest + yearPrincipal;
            final endBalance = yearPayments.isNotEmpty
                ? yearPayments.last.remainingBalance
                : 0.0;

            return DataRow(
              cells: [
                DataCell(Text(year.toString())),
                DataCell(Text(yearPayments.length.toString())),
                DataCell(
                  Text(Formatters.formatCurrency(totalPaid, loan.currency)),
                ),
                DataCell(
                  Text(Formatters.formatCurrency(yearPrincipal, loan.currency)),
                ),
                DataCell(
                  Text(Formatters.formatCurrency(yearInterest, loan.currency)),
                ),
                DataCell(
                  Text(Formatters.formatCurrency(endBalance, loan.currency)),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }
}
