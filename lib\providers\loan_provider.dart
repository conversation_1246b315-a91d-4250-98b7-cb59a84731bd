import 'package:flutter/foundation.dart';
import '../models/loan_model.dart';
import '../models/loan_type.dart';
import '../models/currency.dart';
import '../models/payment_schedule.dart';
import '../services/loan_calculator_service.dart';
import '../services/storage_service.dart';

/// Provider for managing loan calculations and saved loans
class LoanProvider extends ChangeNotifier {
  LoanModel _currentLoan = LoanModel.create(
    name: 'New Loan',
    loanType: LoanType.personal,
  );

  List<LoanModel> _savedLoans = [];
  PaymentSchedule? _currentSchedule;
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  LoanModel get currentLoan => _currentLoan;
  List<LoanModel> get savedLoans => List.unmodifiable(_savedLoans);
  PaymentSchedule? get currentSchedule => _currentSchedule;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasValidLoan => _currentLoan.isValid;

  /// Updates the current loan with new values
  void updateLoan({
    String? name,
    LoanType? loanType,
    double? loanAmount,
    double? annualInterestRate,
    int? termInMonths,
    double? downPayment,
    Currency? currency,
  }) {
    _currentLoan = _currentLoan.copyWith(
      name: name,
      loanType: loanType,
      loanAmount: loanAmount,
      annualInterestRate: annualInterestRate,
      termInMonths: termInMonths,
      downPayment: downPayment,
      currency: currency,
    );
    
    _clearError();
    _calculateSchedule();
    notifyListeners();
  }

  /// Sets a new current loan
  void setCurrentLoan(LoanModel loan) {
    _currentLoan = loan;
    _clearError();
    _calculateSchedule();
    notifyListeners();
  }

  /// Creates a new loan with default values
  void createNewLoan({
    String name = 'New Loan',
    LoanType loanType = LoanType.personal,
    Currency? currency,
  }) {
    _currentLoan = LoanModel.create(
      name: name,
      loanType: loanType,
      currency: currency ?? _currentLoan.currency,
    );
    _currentSchedule = null;
    _clearError();
    notifyListeners();
  }

  /// Calculates the amortization schedule for the current loan
  void _calculateSchedule() {
    if (!_currentLoan.isValid) {
      _currentSchedule = null;
      return;
    }

    try {
      _currentSchedule = LoanCalculatorService.calculateAmortizationSchedule(_currentLoan);
    } catch (e) {
      _setError('Error calculating loan schedule: $e');
      _currentSchedule = null;
    }
  }

  /// Saves the current loan
  Future<bool> saveCurrentLoan() async {
    if (!_currentLoan.isValid) {
      _setError('Cannot save invalid loan');
      return false;
    }

    _setLoading(true);
    
    try {
      final success = await StorageService.saveLoan(_currentLoan);
      if (success) {
        await loadSavedLoans();
        _clearError();
      } else {
        _setError('Failed to save loan');
      }
      return success;
    } catch (e) {
      _setError('Error saving loan: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Loads all saved loans
  Future<void> loadSavedLoans() async {
    _setLoading(true);
    
    try {
      _savedLoans = await StorageService.getSavedLoans();
      _clearError();
    } catch (e) {
      _setError('Error loading saved loans: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Deletes a saved loan
  Future<bool> deleteSavedLoan(String loanId) async {
    _setLoading(true);
    
    try {
      final success = await StorageService.deleteLoan(loanId);
      if (success) {
        await loadSavedLoans();
        _clearError();
      } else {
        _setError('Failed to delete loan');
      }
      return success;
    } catch (e) {
      _setError('Error deleting loan: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Clears all saved loans
  Future<bool> clearAllSavedLoans() async {
    _setLoading(true);
    
    try {
      final success = await StorageService.clearAllLoans();
      if (success) {
        _savedLoans.clear();
        _clearError();
        notifyListeners();
      } else {
        _setError('Failed to clear loans');
      }
      return success;
    } catch (e) {
      _setError('Error clearing loans: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Compares two loans
  Map<String, dynamic> compareLoans(LoanModel loan1, LoanModel loan2) {
    try {
      return LoanCalculatorService.compareLoans(loan1, loan2);
    } catch (e) {
      _setError('Error comparing loans: $e');
      return {};
    }
  }

  /// Calculates loan amount for desired monthly payment
  double calculateLoanAmountForPayment({
    required double desiredMonthlyPayment,
    required double annualInterestRate,
    required int termInMonths,
    double downPayment = 0.0,
  }) {
    try {
      return LoanCalculatorService.calculateLoanAmountForPayment(
        desiredMonthlyPayment: desiredMonthlyPayment,
        annualInterestRate: annualInterestRate,
        termInMonths: termInMonths,
        downPayment: downPayment,
      );
    } catch (e) {
      _setError('Error calculating loan amount: $e');
      return 0.0;
    }
  }

  /// Duplicates the current loan with a new ID
  void duplicateCurrentLoan() {
    final duplicatedLoan = _currentLoan.copyWith(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: '${_currentLoan.name} (Copy)',
      createdAt: DateTime.now(),
      updatedAt: null,
    );
    
    setCurrentLoan(duplicatedLoan);
  }

  /// Sets loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Sets error message
  void _setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// Clears error message
  void _clearError() {
    _errorMessage = null;
  }

  /// Gets a saved loan by ID
  LoanModel? getSavedLoanById(String id) {
    try {
      return _savedLoans.firstWhere((loan) => loan.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Checks if current loan is saved
  bool get isCurrentLoanSaved {
    return _savedLoans.any((loan) => loan.id == _currentLoan.id);
  }

  /// Gets the number of saved loans
  int get savedLoansCount => _savedLoans.length;
}
