import 'package:json_annotation/json_annotation.dart';

/// Enumeration of supported currencies
@JsonEnum()
enum Currency {
  usd('USD', '\$', 'US Dollar'),
  eur('EUR', '€', 'Euro'),
  gbp('GBP', '£', 'British Pound'),
  cad('CAD', 'C\$', 'Canadian Dollar'),
  aud('AUD', 'A\$', 'Australian Dollar'),
  jpy('JPY', '¥', 'Japanese Yen'),
  inr('INR', '₹', 'Indian Rupee'),
  cny('CNY', '¥', 'Chinese Yuan'),
  sar('SAR', 'ر.س', 'Saudi Riyal'),
  omr('OMR', 'ر.ع.', 'Omani Rial');

  const Currency(this.code, this.symbol, this.name);

  final String code;
  final String symbol;
  final String name;

  /// Returns the display name with symbol
  String get displayName => '$name ($symbol)';

  /// Formats an amount with the currency symbol
  String formatAmount(double amount) {
    final places = decimalPlaces;

    // For Arabic currencies (SAR, OMR), put symbol after the amount
    if (this == Currency.sar || this == Currency.omr) {
      return '${amount.toStringAsFixed(places)} $symbol';
    }

    // For other currencies, put symbol before the amount
    return '$symbol${amount.toStringAsFixed(places)}';
  }

  /// Returns the number of decimal places for this currency
  int get decimalPlaces {
    switch (this) {
      case Currency.jpy:
        return 0; // Japanese Yen doesn't use decimal places
      case Currency.omr:
        return 3; // Omani Rial uses 3 decimal places (1000 baisa = 1 OMR)
      default:
        return 2; // Most currencies including SAR use 2 decimal places
    }
  }
}
