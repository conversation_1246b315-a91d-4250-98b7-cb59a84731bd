/// Enumeration of supported currencies
enum Currency {
  usd('USD', '\$', 'US Dollar'),
  eur('EUR', '€', 'Euro'),
  gbp('GBP', '£', 'British Pound'),
  cad('CAD', 'C\$', 'Canadian Dollar'),
  aud('AUD', 'A\$', 'Australian Dollar'),
  jpy('JPY', '¥', 'Japanese Yen'),
  inr('INR', '₹', 'Indian Rupee'),
  cny('CNY', '¥', 'Chinese Yuan');

  const Currency(this.code, this.symbol, this.name);

  final String code;
  final String symbol;
  final String name;

  /// Returns the display name with symbol
  String get displayName => '$name ($symbol)';

  /// Formats an amount with the currency symbol
  String formatAmount(double amount) {
    // For JPY and similar currencies, don't show decimal places
    if (this == Currency.jpy) {
      return '$symbol${amount.toStringAsFixed(0)}';
    }
    return '$symbol${amount.toStringAsFixed(2)}';
  }

  /// Returns the number of decimal places for this currency
  int get decimalPlaces {
    switch (this) {
      case Currency.jpy:
        return 0;
      default:
        return 2;
    }
  }
}
