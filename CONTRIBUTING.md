# Contributing to Bank Loan Calculator

Thank you for your interest in contributing to the Bank Loan Calculator! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (3.8.1 or higher)
- Dart SDK (3.8.1 or higher)
- Git
- IDE with Flutter support (VS Code, Android Studio, or IntelliJ)

### Development Setup
1. **Fork the repository**
   ```bash
   git clone https://github.com/your-username/bank-loan-calculator.git
   cd bank-loan-calculator
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

## 📋 Development Guidelines

### Code Style
- Follow [Dart Style Guide](https://dart.dev/guides/language/effective-dart/style)
- Use `flutter format .` to format code
- Run `flutter analyze` to check for issues
- Follow the existing project structure and naming conventions

### Project Structure
```
lib/
├── models/           # Data models and entities
├── services/         # Business logic and external services
├── providers/        # State management (Provider pattern)
├── screens/          # UI screens and pages
├── widgets/          # Reusable UI components
└── utils/           # Utility functions and helpers
```

### Naming Conventions
- **Files**: Use snake_case (e.g., `loan_calculator_service.dart`)
- **Classes**: Use PascalCase (e.g., `LoanCalculatorService`)
- **Variables/Functions**: Use camelCase (e.g., `calculateMonthlyPayment`)
- **Constants**: Use SCREAMING_SNAKE_CASE (e.g., `DEFAULT_CURRENCY`)

## 🧪 Testing

### Running Tests
```bash
# Run all tests
flutter test

# Run specific test files
flutter test test/unit/loan_calculator_test.dart
flutter test test/widget/

# Run tests with coverage
flutter test --coverage
```

### Writing Tests
- **Unit Tests**: Test business logic and calculations
- **Widget Tests**: Test UI components and user interactions
- **Integration Tests**: Test complete user workflows

### Test Guidelines
- Write tests for all new features
- Maintain test coverage above 80%
- Use descriptive test names
- Test edge cases and error conditions

## 🔧 Making Changes

### Branch Naming
- `feature/description` - New features
- `bugfix/description` - Bug fixes
- `hotfix/description` - Critical fixes
- `docs/description` - Documentation updates

### Commit Messages
Follow [Conventional Commits](https://www.conventionalcommits.org/):
```
type(scope): description

feat(calculator): add support for bi-weekly payments
fix(ui): resolve overflow in currency dropdown
docs(readme): update installation instructions
test(unit): add tests for edge cases
```

### Pull Request Process
1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Write clean, well-documented code
   - Add tests for new functionality
   - Update documentation if needed

3. **Test your changes**
   ```bash
   flutter test
   flutter analyze
   flutter format .
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat(scope): description"
   ```

5. **Push to your fork**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create a Pull Request**
   - Use a descriptive title
   - Explain what changes you made and why
   - Reference any related issues
   - Include screenshots for UI changes

## 🐛 Reporting Issues

### Bug Reports
When reporting bugs, please include:
- **Description**: Clear description of the issue
- **Steps to Reproduce**: Detailed steps to reproduce the bug
- **Expected Behavior**: What you expected to happen
- **Actual Behavior**: What actually happened
- **Environment**: Flutter version, platform, device info
- **Screenshots**: If applicable

### Feature Requests
When requesting features, please include:
- **Description**: Clear description of the feature
- **Use Case**: Why this feature would be useful
- **Proposed Solution**: How you think it should work
- **Alternatives**: Any alternative solutions considered

## 📝 Documentation

### Code Documentation
- Add dartdoc comments for public APIs
- Include examples in documentation
- Document complex algorithms and business logic
- Keep documentation up to date with code changes

### README Updates
- Update README.md for new features
- Include usage examples
- Update installation instructions if needed

## 🎯 Areas for Contribution

### High Priority
- **Performance Optimization**: Improve calculation speed
- **Accessibility**: Enhance screen reader support
- **Internationalization**: Add support for more languages
- **Testing**: Increase test coverage

### Medium Priority
- **UI/UX Improvements**: Better user experience
- **New Features**: Additional loan types or calculations
- **Documentation**: Improve guides and examples
- **Code Quality**: Refactoring and optimization

### Good First Issues
- **Bug Fixes**: Small, well-defined bugs
- **Documentation**: Improve existing documentation
- **Tests**: Add missing test cases
- **UI Polish**: Minor UI improvements

## 🔍 Code Review Guidelines

### For Contributors
- Keep pull requests focused and small
- Write clear commit messages
- Add tests for new functionality
- Update documentation as needed

### For Reviewers
- Be constructive and helpful
- Focus on code quality and maintainability
- Check for proper testing
- Verify documentation updates

## 📚 Resources

### Flutter Resources
- [Flutter Documentation](https://docs.flutter.dev/)
- [Dart Language Guide](https://dart.dev/guides)
- [Flutter Widget Catalog](https://docs.flutter.dev/development/ui/widgets)

### Project-Specific Resources
- [Architecture Guide](docs/architecture.md)
- [API Documentation](docs/api.md)
- [Testing Guide](docs/testing.md)

## 🤝 Community

### Communication
- **GitHub Issues**: For bug reports and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Pull Requests**: For code contributions

### Code of Conduct
- Be respectful and inclusive
- Help others learn and grow
- Focus on constructive feedback
- Maintain a professional environment

## 📄 License

By contributing to this project, you agree that your contributions will be licensed under the MIT License.

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- CHANGELOG.md for significant contributions
- GitHub contributors page

Thank you for contributing to the Bank Loan Calculator! 🎉
