import 'package:flutter/material.dart';

/// Provider for managing navigation between tabs
class NavigationProvider extends ChangeNotifier {
  int _currentTabIndex = 0;

  int get currentTabIndex => _currentTabIndex;

  void switchToTab(int index) {
    if (_currentTabIndex != index) {
      _currentTabIndex = index;
      notifyListeners();
    }
  }

  void switchToCalculatorTab() {
    switchToTab(0);
  }

  void switchToSavedLoansTab() {
    switchToTab(1);
  }

  void switchToSettingsTab() {
    switchToTab(2);
  }
}
