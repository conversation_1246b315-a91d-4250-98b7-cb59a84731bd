import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/loan_provider.dart';
import '../widgets/simple_loan_input_form.dart';
import '../widgets/simple_results_summary.dart';

/// Simple home screen without complex navigation
class SimpleHomeScreen extends StatelessWidget {
  const SimpleHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Loan Calculator'),
        centerTitle: true,
      ),
      body: Consumer<LoanProvider>(
        builder: (context, loanProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Error message display
                if (loanProvider.errorMessage != null)
                  Card(
                    color: Theme.of(context).colorScheme.errorContainer,
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Theme.of(context).colorScheme.onErrorContainer,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              loanProvider.errorMessage!,
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.onErrorContainer,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                const SizedBox(height: 16),

                // Loan input form
                const SimpleLoanInputForm(),

                const SizedBox(height: 24),

                // Results summary
                if (loanProvider.hasValidLoan) ...[
                  const SimpleResultsSummary(),
                  const SizedBox(height: 16),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _showDetailedResults(context),
                          icon: const Icon(Icons.analytics),
                          label: const Text('View Details'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _saveLoan(context),
                          icon: const Icon(Icons.save),
                          label: const Text('Save Loan'),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Additional action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _duplicateLoan(context),
                          icon: const Icon(Icons.copy),
                          label: const Text('Duplicate'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _newLoan(context),
                          icon: const Icon(Icons.add),
                          label: const Text('New Loan'),
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: 32),
              ],
            ),
          );
        },
      ),
    );
  }

  void _showDetailedResults(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Detailed Results'),
        content: const Text('Detailed results feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _saveLoan(BuildContext context) async {
    final loanProvider = context.read<LoanProvider>();
    final success = await loanProvider.saveCurrentLoan();

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            success ? 'Loan saved successfully!' : 'Failed to save loan',
          ),
          backgroundColor: success
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  void _duplicateLoan(BuildContext context) {
    context.read<LoanProvider>().duplicateCurrentLoan();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Loan duplicated')),
    );
  }

  void _newLoan(BuildContext context) {
    context.read<LoanProvider>().createNewLoan();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('New loan created')),
    );
  }
}
