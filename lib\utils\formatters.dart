import 'package:intl/intl.dart';
import '../models/currency.dart';

/// Utility class for formatting numbers, currencies, and other display values
class Formatters {
  /// Formats a currency amount with the specified currency
  static String formatCurrency(double amount, Currency currency) {
    final formatter = NumberFormat.currency(
      symbol: currency.symbol,
      decimalDigits: currency.decimalPlaces,
    );
    return formatter.format(amount);
  }

  /// Formats a percentage value
  static String formatPercentage(double value, {int decimalPlaces = 2}) {
    return '${value.toStringAsFixed(decimalPlaces)}%';
  }

  /// Formats a number with thousand separators
  static String formatNumber(double value, {int decimalPlaces = 2}) {
    final formatter = NumberFormat('#,##0.${'0' * decimalPlaces}');
    return formatter.format(value);
  }

  /// Formats a duration in months to a readable string
  static String formatLoanTerm(int months) {
    if (months < 12) {
      return '$months month${months == 1 ? '' : 's'}';
    }

    final years = months ~/ 12;
    final remainingMonths = months % 12;

    String result = '$years year${years == 1 ? '' : 's'}';

    if (remainingMonths > 0) {
      result += ' $remainingMonths month${remainingMonths == 1 ? '' : 's'}';
    }

    return result;
  }

  /// Formats a date for display
  static String formatDate(DateTime? date) {
    // Null check was added here
    if (date == null) {
      return 'N/A'; // Or some other placeholder for an invalid date
    }
    final formatter = DateFormat('MMM dd, yyyy');
    return formatter.format(date);
  }

  /// Formats a date for month/year display
  static String formatMonthYear(DateTime date) {
    final formatter = DateFormat('MMM yyyy');
    return formatter.format(date);
  }

  /// Formats a large number with appropriate suffixes (K, M, B)
  static String formatLargeNumber(double value, {int decimalPlaces = 1}) {
    if (value >= 1000000000) {
      return '${(value / 1000000000).toStringAsFixed(decimalPlaces)}B';
    } else if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(decimalPlaces)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(decimalPlaces)}K';
    } else {
      return value.toStringAsFixed(decimalPlaces);
    }
  }

  /// Parses a currency string to double
  static double parseCurrency(String value) {
    // Remove currency symbols and formatting
    String cleanValue = value.replaceAll(RegExp(r'[^\d.-]'), '');
    return double.tryParse(cleanValue) ?? 0.0;
  }

  /// Parses a percentage string to double
  static double parsePercentage(String value) {
    String cleanValue = value.replaceAll('%', '').trim();
    return double.tryParse(cleanValue) ?? 0.0;
  }

  /// Validates if a string is a valid number
  static bool isValidNumber(String value) {
    return double.tryParse(value) != null;
  }

  /// Validates if a string is a valid positive number
  static bool isValidPositiveNumber(String value) {
    final number = double.tryParse(value);
    return number != null && number > 0;
  }

  /// Validates if a string is a valid percentage (0-100)
  static bool isValidPercentage(String value) {
    final number = parsePercentage(value);
    return number >= 0 && number <= 100;
  }

  /// Formats a payment frequency
  static String formatPaymentFrequency(int paymentsPerYear) {
    switch (paymentsPerYear) {
      case 12:
        return 'Monthly';
      case 26:
        return 'Bi-weekly';
      case 24:
        return 'Semi-monthly';
      case 4:
        return 'Quarterly';
      case 2:
        return 'Semi-annually';
      case 1:
        return 'Annually';
      default:
        return '$paymentsPerYear times per year';
    }
  }

  /// Formats a difference value with + or - prefix
  static String formatDifference(double value, Currency currency) {
    final prefix = value >= 0 ? '+' : '';
    return '$prefix${formatCurrency(value, currency)}';
  }

  /// Formats a ratio as a percentage
  static String formatRatio(double ratio, {int decimalPlaces = 1}) {
    return '${(ratio * 100).toStringAsFixed(decimalPlaces)}%';
  }

  /// Formats file size in bytes to human readable format
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Capitalizes the first letter of a string
  static String capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1).toLowerCase();
  }

  /// Formats a loan summary for display
  static String formatLoanSummary(
    double loanAmount,
    double interestRate,
    int termInMonths,
    Currency currency,
  ) {
    return '${formatCurrency(loanAmount, currency)} at '
        '${formatPercentage(interestRate)} for '
        '${formatLoanTerm(termInMonths)}';
  }
}
