import '../models/currency.dart';

/// Utility class for currency-related operations
class CurrencyUtils {
  /// Get currency name
  static String getCurrencyName(Currency currency) {
    switch (currency) {
      case Currency.usd:
        return 'US Dollar';
      case Currency.eur:
        return 'Euro';
      case Currency.gbp:
        return 'British Pound';
      case Currency.cad:
        return 'Canadian Dollar';
      case Currency.aud:
        return 'Australian Dollar';
      case Currency.jpy:
        return 'Japanese Yen';
      case Currency.inr:
        return 'Indian Rupee';
      case Currency.cny:
        return 'Chinese Yuan';
      case Currency.sar:
        return 'Saudi Riyal';
      case Currency.omr:
        return 'Omani Rial';
    }
  }

  /// Get currency display name with symbol
  static String getCurrencyDisplayName(Currency currency) {
    final name = getCurrencyName(currency);
    return '$name (${currency.symbol})';
  }

  /// Get currency flag emoji
  static String getCurrencyFlag(Currency currency) {
    switch (currency) {
      case Currency.usd:
        return '🇺🇸';
      case Currency.eur:
        return '🇪🇺';
      case Currency.gbp:
        return '🇬🇧';
      case Currency.cad:
        return '🇨🇦';
      case Currency.aud:
        return '🇦🇺';
      case Currency.jpy:
        return '🇯🇵';
      case Currency.inr:
        return '🇮🇳';
      case Currency.cny:
        return '🇨🇳';
      case Currency.sar:
        return '🇸🇦';
      case Currency.omr:
        return '🇴🇲';
    }
  }

  /// Format amount with proper decimal places for the currency
  static String formatCurrencyAmount(
    Currency currency,
    double amount, {
    int? decimalPlaces,
  }) {
    final places = decimalPlaces ?? currency.decimalPlaces;

    // For currencies that use RTL symbols (Arabic currencies)
    if (currency == Currency.sar || currency == Currency.omr) {
      return '${amount.toStringAsFixed(places)} ${currency.symbol}';
    }

    // For other currencies, symbol comes first
    return '${currency.symbol}${amount.toStringAsFixed(places)}';
  }



  /// Check if currency uses RTL formatting
  static bool isRTLCurrency(Currency currency) {
    return currency == Currency.sar || currency == Currency.omr;
  }

  /// Get appropriate decimal places for currency input
  static int getInputDecimalPlaces(Currency currency) {
    // For display purposes, we might want to limit decimal places in input
    switch (currency) {
      case Currency.jpy:
        return 0;
      case Currency.omr:
        return 3;
      default:
        return 2;
    }
  }

  /// Validate currency amount based on currency rules
  static bool isValidAmount(Currency currency, double amount) {
    if (amount < 0) return false;

    // Check for reasonable maximum amounts
    switch (currency) {
      case Currency.jpy:
        return amount <= 999999999; // 999 million JPY
      case Currency.omr:
        return amount <= 9999999.999; // ~10 million OMR
      default:
        return amount <= 99999999.99; // ~100 million for most currencies
    }
  }

  /// Get minimum loan amount for currency
  static double getMinimumLoanAmount(Currency currency) {
    switch (currency) {
      case Currency.jpy:
        return 10000; // 10,000 JPY
      case Currency.omr:
        return 100; // 100 OMR
      case Currency.sar:
        return 1000; // 1,000 SAR
      case Currency.inr:
        return 10000; // 10,000 INR
      default:
        return 100; // 100 for USD, EUR, GBP, etc.
    }
  }

  /// Get typical loan amount suggestions for currency
  static List<double> getLoanAmountSuggestions(Currency currency) {
    switch (currency) {
      case Currency.jpy:
        return [1000000, 5000000, 10000000, 20000000]; // 1M, 5M, 10M, 20M JPY
      case Currency.omr:
        return [5000, 10000, 25000, 50000]; // 5K, 10K, 25K, 50K OMR
      case Currency.sar:
        return [50000, 100000, 250000, 500000]; // 50K, 100K, 250K, 500K SAR
      case Currency.inr:
        return [500000, 1000000, 2500000, 5000000]; // 500K, 1M, 2.5M, 5M INR
      case Currency.cny:
        return [100000, 500000, 1000000, 2000000]; // 100K, 500K, 1M, 2M CNY
      default:
        return [
          10000,
          25000,
          50000,
          100000,
        ]; // 10K, 25K, 50K, 100K for USD, EUR, etc.
    }
  }
}
