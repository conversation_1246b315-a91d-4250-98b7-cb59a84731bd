// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:bank_loan_calculator/main.dart';
import 'package:bank_loan_calculator/providers/loan_provider.dart';
import 'package:bank_loan_calculator/providers/settings_provider.dart';

void main() {
  testWidgets('App loads and displays calculator', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => LoanProvider()),
          ChangeNotifierProvider(create: (_) => SettingsProvider()),
        ],
        child: const BankLoanCalculatorApp(),
      ),
    );

    // Verify that the app loads with the calculator tab
    expect(find.text('Loan Calculator'), findsOneWidget);
    expect(find.text('Calculator'), findsOneWidget);

    // Verify navigation tabs are present
    expect(find.text('Saved Loans'), findsOneWidget);
    expect(find.text('Settings'), findsOneWidget);
  });

  testWidgets('Navigation between tabs works', (WidgetTester tester) async {
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => LoanProvider()),
          ChangeNotifierProvider(create: (_) => SettingsProvider()),
        ],
        child: const BankLoanCalculatorApp(),
      ),
    );

    // Tap on Saved Loans tab
    await tester.tap(find.text('Saved Loans'));
    await tester.pumpAndSettle();

    // Should show saved loans screen
    expect(find.text('No Saved Loans'), findsOneWidget);

    // Tap on Settings tab
    await tester.tap(find.text('Settings'));
    await tester.pumpAndSettle();

    // Should show settings screen
    expect(find.text('Currency & Display'), findsOneWidget);
  });
}
