{"@@locale": "en", "appTitle": "Bank Loan Calculator", "@appTitle": {"description": "The title of the application"}, "calculator": "Calculator", "@calculator": {"description": "Calculator tab label"}, "savedLoans": "Saved Loans", "@savedLoans": {"description": "Saved loans tab label"}, "settings": "Settings", "@settings": {"description": "Settings tab label"}, "loanCalculator": "Loan Calculator", "@loanCalculator": {"description": "Loan calculator screen title"}, "loanDetails": "<PERSON>an <PERSON>", "@loanDetails": {"description": "Loan details section title"}, "loanName": "Loan Name", "@loanName": {"description": "Loan name field label"}, "enterLoanName": "Enter a name for this loan", "@enterLoanName": {"description": "Loan name field hint"}, "pleaseEnterLoanName": "Please enter a loan name", "@pleaseEnterLoanName": {"description": "Loan name validation message"}, "loanType": "Loan Type", "@loanType": {"description": "Loan type field label"}, "currency": "<PERSON><PERSON><PERSON><PERSON>", "@currency": {"description": "Currency field label"}, "loanAmount": "<PERSON><PERSON>", "@loanAmount": {"description": "Loan amount field label"}, "enterLoanAmount": "Enter loan amount", "@enterLoanAmount": {"description": "Loan amount field hint"}, "pleaseEnterLoanAmount": "Please enter loan amount", "@pleaseEnterLoanAmount": {"description": "Loan amount validation message"}, "pleaseEnterValidAmount": "Please enter a valid amount", "@pleaseEnterValidAmount": {"description": "Loan amount validation message for invalid values"}, "annualInterestRate": "Annual Interest Rate", "@annualInterestRate": {"description": "Interest rate field label"}, "enterInterestRate": "Enter interest rate", "@enterInterestRate": {"description": "Interest rate field hint"}, "pleaseEnterInterestRate": "Please enter interest rate", "@pleaseEnterInterestRate": {"description": "Interest rate validation message"}, "pleaseEnterValidRate": "Please enter a valid rate (0-100%)", "@pleaseEnterValidRate": {"description": "Interest rate validation message for invalid values"}, "loanTerm": "<PERSON>an <PERSON>", "@loanTerm": {"description": "Loan term field label"}, "enterLoanTerm": "Enter loan term", "@enterLoanTerm": {"description": "Loan term field hint"}, "pleaseEnterLoanTerm": "Please enter loan term", "@pleaseEnterLoanTerm": {"description": "Loan term validation message"}, "pleaseEnterValidTerm": "Please enter a valid term", "@pleaseEnterValidTerm": {"description": "Loan term validation message for invalid values"}, "years": "Years", "@years": {"description": "Years label"}, "months": "Months", "@months": {"description": "Months label"}, "downPayment": "Down Payment (Optional)", "@downPayment": {"description": "Down payment field label"}, "enterDownPayment": "Enter down payment", "@enterDownPayment": {"description": "Down payment field hint"}, "pleaseEnterValidDownPayment": "Please enter a valid down payment", "@pleaseEnterValidDownPayment": {"description": "Down payment validation message"}, "downPaymentMustBeLess": "Down payment must be less than loan amount", "@downPaymentMustBeLess": {"description": "Down payment validation message for amount too high"}, "calculationResults": "Calculation Results", "@calculationResults": {"description": "Results section title"}, "monthlyPayment": "Monthly Payment", "@monthlyPayment": {"description": "Monthly payment label"}, "principalAmount": "Principal Amount", "@principalAmount": {"description": "Principal amount label"}, "totalInterestPaid": "Total Interest Paid", "@totalInterestPaid": {"description": "Total interest label"}, "totalAmountPaid": "Total Amount Paid", "@totalAmountPaid": {"description": "Total amount label"}, "paymentBreakdown": "Payment Breakdown", "@paymentBreakdown": {"description": "Payment breakdown section title"}, "principal": "Principal", "@principal": {"description": "Principal label in breakdown"}, "interest": "Interest", "@interest": {"description": "Interest label in breakdown"}, "viewDetails": "View Details", "@viewDetails": {"description": "View details button"}, "saveLoan": "Save Loan", "@saveLoan": {"description": "Save loan button"}, "duplicate": "Duplicate", "@duplicate": {"description": "Duplicate button"}, "newLoan": "New Loan", "@newLoan": {"description": "New loan button"}, "loanSavedSuccessfully": "<PERSON><PERSON> saved successfully!", "@loanSavedSuccessfully": {"description": "Success message for saving loan"}, "failedToSaveLoan": "Failed to save loan", "@failedToSaveLoan": {"description": "Error message for saving loan"}, "loanDuplicated": "Loan duplicated", "@loanDuplicated": {"description": "Success message for duplicating loan"}, "newLoanCreated": "New loan created", "@newLoanCreated": {"description": "Success message for creating new loan"}, "personalLoan": "Personal Loan", "@personalLoan": {"description": "Personal loan type"}, "mortgage": "Mortgage", "@mortgage": {"description": "Mortgage loan type"}, "autoLoan": "Auto Loan", "@autoLoan": {"description": "Auto loan type"}, "businessLoan": "Business Loan", "@businessLoan": {"description": "Business loan type"}, "noSavedLoans": "No Saved Loans", "@noSavedLoans": {"description": "Empty state message for saved loans"}, "calculateLoanAndSave": "Calculate a loan and save it to see it here", "@calculateLoanAndSave": {"description": "Empty state description for saved loans"}, "compareLoans": "Compare Loans", "@compareLoans": {"description": "Compare loans button"}, "clearAll": "Clear All", "@clearAll": {"description": "Clear all button"}, "exportAll": "Export All", "@exportAll": {"description": "Export all button"}, "delete": "Delete", "@delete": {"description": "Delete button"}, "deleteLoan": "Delete Loan", "@deleteLoan": {"description": "Delete loan dialog title"}, "areYouSureDeleteLoan": "Are you sure you want to delete this loan?", "@areYouSureDeleteLoan": {"description": "Delete loan confirmation message"}, "areYouSureDeleteLoans": "Are you sure you want to delete {count} loans?", "@areYouSureDeleteLoans": {"description": "Delete multiple loans confirmation message", "placeholders": {"count": {"type": "int"}}}, "cancel": "Cancel", "@cancel": {"description": "Cancel button"}, "currencyAndDisplay": "Currency & Display", "@currencyAndDisplay": {"description": "Settings section title"}, "defaultCurrency": "<PERSON><PERSON><PERSON>", "@defaultCurrency": {"description": "Default currency setting"}, "decimalPlaces": "Decimal Places", "@decimalPlaces": {"description": "Decimal places setting"}, "display": "Display", "@display": {"description": "Display settings section"}, "darkMode": "Dark Mode", "@darkMode": {"description": "Dark mode setting"}, "useDarkTheme": "Use dark theme", "@useDarkTheme": {"description": "Dark mode description"}, "detailedResults": "Detailed Results", "@detailedResults": {"description": "Detailed results setting"}, "showDetailedResults": "Show detailed calculation results", "@showDetailedResults": {"description": "Detailed results description"}, "calculations": "Calculations", "@calculations": {"description": "Calculations settings section"}, "autoSaveCalculations": "Auto-save Calculations", "@autoSaveCalculations": {"description": "Auto-save setting"}, "automaticallySave": "Automatically save loan calculations", "@automaticallySave": {"description": "Auto-save description"}, "dataManagement": "Data Management", "@dataManagement": {"description": "Data management section"}, "exportData": "Export Data", "@exportData": {"description": "Export data option"}, "exportAllSavedLoans": "Export all saved loans", "@exportAllSavedLoans": {"description": "Export data description"}, "importData": "Import Data", "@importData": {"description": "Import data option"}, "importSavedLoans": "Import saved loans", "@importSavedLoans": {"description": "Import data description"}, "clearAllData": "Clear All Data", "@clearAllData": {"description": "Clear all data option"}, "deleteAllSavedLoans": "Delete all saved loans and settings", "@deleteAllSavedLoans": {"description": "Clear all data description"}, "about": "About", "@about": {"description": "About section"}, "appVersion": "App Version", "@appVersion": {"description": "App version label"}, "helpAndSupport": "Help & Support", "@helpAndSupport": {"description": "Help and support option"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Privacy policy option"}, "resetToDefaults": "Reset to Defaults", "@resetToDefaults": {"description": "Reset settings button"}, "language": "Language", "@language": {"description": "Language setting"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Language selection dialog title"}, "english": "English", "@english": {"description": "English language option"}, "arabic": "العربية", "@arabic": {"description": "Arabic language option"}, "summary": "Summary", "@summary": {"description": "Summary tab label"}, "schedule": "Schedule", "@schedule": {"description": "Schedule tab label"}, "quickStatistics": "Quick Statistics", "@quickStatistics": {"description": "Quick statistics section title"}, "firstPayment": "First Payment", "@firstPayment": {"description": "First payment label"}, "finalPayment": "Final Payment", "@finalPayment": {"description": "Final payment label"}, "interestRate": "Interest Rate", "@interestRate": {"description": "Interest rate label"}, "totalPayments": "Total Payments", "@totalPayments": {"description": "Total payments label"}, "yearlySummary": "Yearly Summary", "@yearlySummary": {"description": "Yearly summary section title"}, "shareResults": "Share Results", "@shareResults": {"description": "Share results button"}, "exportAsPdf": "Export as PDF", "@exportAsPdf": {"description": "Export as PDF button"}, "monthly": "Monthly", "@monthly": {"description": "Monthly view option"}, "yearly": "Yearly", "@yearly": {"description": "Yearly view option"}, "allYears": "All Years", "@allYears": {"description": "All years filter option"}, "paymentNumber": "Payment #", "@paymentNumber": {"description": "Payment number column header"}, "date": "Date", "@date": {"description": "Date column header"}, "payment": "Payment", "@payment": {"description": "Payment column header"}, "balance": "Balance", "@balance": {"description": "Balance column header"}, "payments": "Payments", "@payments": {"description": "Payments column header"}, "totalPaid": "Total Paid", "@totalPaid": {"description": "Total paid column header"}, "endBalance": "End Balance", "@endBalance": {"description": "End balance column header"}, "loanComparison": "<PERSON><PERSON>", "@loanComparison": {"description": "Loan comparison screen title"}, "quickComparison": "Quick Comparison", "@quickComparison": {"description": "Quick comparison section title"}, "difference": "Difference", "@difference": {"description": "Difference label in comparison"}, "detailedComparison": "Detailed Comparison", "@detailedComparison": {"description": "Detailed comparison section title"}, "recommendation": "Recommendation", "@recommendation": {"description": "Recommendation section title"}, "bothLoansAreSimilar": "Both loans are very similar", "@bothLoansAreSimilar": {"description": "Recommendation when loans are similar"}, "loanAIsBetter": "Loan <PERSON> is better overall", "@loanAIsBetter": {"description": "Recommendation when loan A is better"}, "loanBIsBetter": "Loan B is better overall", "@loanBIsBetter": {"description": "Recommendation when loan B is better"}, "note": "Note", "@note": {"description": "Note label"}, "recommendationNote": "This recommendation is based purely on financial calculations. Consider other factors such as lender reputation, customer service, and loan terms.", "@recommendationNote": {"description": "Recommendation disclaimer note"}, "created": "Created", "@created": {"description": "Created date label"}, "amount": "Amount", "@amount": {"description": "Amount label"}, "rate": "Rate", "@rate": {"description": "Rate label"}, "term": "Term", "@term": {"description": "Term label"}, "perMonth": "per month", "@perMonth": {"description": "Per month suffix"}, "selectCurrency": "Select Currency", "@selectCurrency": {"description": "Select currency dialog title"}, "close": "Close", "@close": {"description": "Close button"}, "gotIt": "Got it", "@gotIt": {"description": "Got it button"}, "howToUse": "How to Use", "@howToUse": {"description": "How to use dialog title"}, "howToUseContent": "1. Select your loan type\n2. Enter the loan amount\n3. Set the annual interest rate\n4. Choose the loan term\n5. Add down payment if applicable\n6. View calculated results\n7. Save or export your calculation", "@howToUseContent": {"description": "How to use content"}, "tips": "Tips", "@tips": {"description": "Tips section title"}, "tipsContent": "• Use the detailed view for amortization schedule\n• Save multiple loan scenarios for comparison\n• Export results as PDF for sharing", "@tipsContent": {"description": "Tips content"}, "help": "Help", "@help": {"description": "Help button"}, "bankLoanCalculator": "Bank Loan Calculator", "@bankLoanCalculator": {"description": "App name in help"}, "helpContent": "This app helps you calculate loan payments, compare different loan options, and understand the total cost of borrowing.", "@helpContent": {"description": "Help content description"}, "features": "Features", "@features": {"description": "Features section title"}, "featuresContent": "• Calculate monthly payments\n• View amortization schedules\n• Compare multiple loans\n• Save and manage loan calculations\n• Export results as PDF", "@featuresContent": {"description": "Features list content"}, "support": "For support, please contact <NAME_EMAIL>", "@support": {"description": "Support contact information"}, "privacyPolicyContent": "This app stores loan calculation data locally on your device. No personal or financial information is transmitted to external servers. All calculations are performed locally and your data remains private.", "@privacyPolicyContent": {"description": "Privacy policy content"}, "resetSettings": "Reset Settings", "@resetSettings": {"description": "Reset settings dialog title"}, "resetSettingsConfirm": "Reset all settings to their default values?", "@resetSettingsConfirm": {"description": "Reset settings confirmation message"}, "reset": "Reset", "@reset": {"description": "Reset button"}, "settingsResetToDefaults": "Settings reset to defaults", "@settingsResetToDefaults": {"description": "Settings reset success message"}, "clearAllLoans": "Clear All Loans", "@clearAllLoans": {"description": "Clear all loans dialog title"}, "clearAllLoansConfirm": "Are you sure you want to delete all saved loans? This action cannot be undone.", "@clearAllLoansConfirm": {"description": "Clear all loans confirmation message"}, "allDataCleared": "All data cleared successfully", "@allDataCleared": {"description": "All data cleared success message"}, "exportFeatureComingSoon": "Export feature coming soon", "@exportFeatureComingSoon": {"description": "Export feature coming soon message"}, "importFeatureComingSoon": "Import feature coming soon", "@importFeatureComingSoon": {"description": "Import feature coming soon message"}, "shareComparisonComingSoon": "Share comparison feature coming soon", "@shareComparisonComingSoon": {"description": "Share comparison coming soon message"}, "exportAllFeatureComingSoon": "Export all feature coming soon", "@exportAllFeatureComingSoon": {"description": "Export all coming soon message"}, "pdfExportedSuccessfully": "PDF exported successfully", "@pdfExportedSuccessfully": {"description": "PDF export success message"}, "errorExportingPdf": "Error exporting PDF", "@errorExportingPdf": {"description": "PDF export error message"}, "errorSharingResults": "Error sharing results", "@errorSharingResults": {"description": "Share results error message"}, "errorComparingLoans": "Error comparing loans", "@errorComparingLoans": {"description": "Compare loans error message"}, "select2LoansToCompare": "Select 2 loans to compare", "@select2LoansToCompare": {"description": "Select loans to compare message"}, "selected": "selected", "@selected": {"description": "Selected items count suffix"}, "compare": "Compare", "@compare": {"description": "Compare button"}, "deleteSelected": "Delete Selected", "@deleteSelected": {"description": "Delete selected button"}, "loanA": "Loan <PERSON>", "@loanA": {"description": "Loan A label"}, "loanB": "Loan B", "@loanB": {"description": "Loan B label"}, "copy": "Copy", "@copy": {"description": "Copy button"}, "currencyUSD": "US Dollar", "@currencyUSD": {"description": "US Dollar currency name"}, "currencyEUR": "Euro", "@currencyEUR": {"description": "Euro currency name"}, "currencyGBP": "British Pound", "@currencyGBP": {"description": "British Pound currency name"}, "currencyCAD": "Canadian Dollar", "@currencyCAD": {"description": "Canadian Dollar currency name"}, "currencyAUD": "Australian Dollar", "@currencyAUD": {"description": "Australian Dollar currency name"}, "currencyJPY": "Japanese Yen", "@currencyJPY": {"description": "Japanese Yen currency name"}, "currencyINR": "Indian Rupee", "@currencyINR": {"description": "Indian Rupee currency name"}, "currencyCNY": "Chinese Yuan", "@currencyCNY": {"description": "Chinese Yuan currency name"}, "currencySAR": "Saudi Riyal", "@currencySAR": {"description": "Saudi Riyal currency name"}, "currencyOMR": "Omani R<PERSON>", "@currencyOMR": {"description": "Omani Rial currency name"}}