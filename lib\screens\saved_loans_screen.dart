import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import '../providers/loan_provider.dart';
import '../models/loan_model.dart';
import '../utils/formatters.dart';
import '../services/loan_calculator_service.dart';
import 'comparison_screen.dart';

/// Screen for managing saved loan calculations
class SavedLoansScreen extends StatefulWidget {
  const SavedLoansScreen({super.key});

  @override
  State<SavedLoansScreen> createState() => _SavedLoansScreenState();
}

class _SavedLoansScreenState extends State<SavedLoansScreen> {
  final Set<String> _selectedLoans = {};
  bool _isSelectionMode = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _isSelectionMode
              ? '${_selectedLoans.length} selected'
              : 'Saved Loans',
        ),
        leading: _isSelectionMode
            ? IconButton(
                icon: const Icon(Icons.close),
                onPressed: _exitSelectionMode,
              )
            : null,
        actions: [
          if (_isSelectionMode) ...[
            if (_selectedLoans.length == 2)
              IconButton(
                icon: const Icon(Icons.compare_arrows),
                onPressed: _compareSelectedLoans,
                tooltip: 'Compare Loans',
              ),
            IconButton(
              icon: const Icon(Icons.delete),
              onPressed: _deleteSelectedLoans,
              tooltip: 'Delete Selected',
            ),
          ] else ...[
            IconButton(
              icon: const Icon(Icons.compare_arrows),
              onPressed: _enterComparisonMode,
              tooltip: 'Compare Loans',
            ),
            PopupMenuButton<String>(
              onSelected: _handleMenuAction,
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'clear_all',
                  child: ListTile(
                    leading: Icon(Icons.clear_all),
                    title: Text('Clear All'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'export_all',
                  child: ListTile(
                    leading: Icon(Icons.file_download),
                    title: Text('Export All'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
      body: Consumer<LoanProvider>(
        builder: (context, loanProvider, child) {
          if (loanProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (loanProvider.savedLoans.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.savings_outlined,
                    size: 64,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No saved loans yet',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Theme.of(context).colorScheme.outline,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Save your loan calculations to view them here',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.outline,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.calculate),
                    label: const Text('Create New Loan'),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: loanProvider.savedLoans.length,
            itemBuilder: (context, index) {
              final loan = loanProvider.savedLoans[index];
              return _buildLoanCard(context, loan, loanProvider);
            },
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No Saved Loans',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            'Calculate a loan and save it to see it here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoanCard(
    BuildContext context,
    LoanModel loan,
    LoanProvider loanProvider,
  ) {
    final isSelected = _selectedLoans.contains(loan.id);
    final monthlyPayment = loan.isValid
        ? LoanCalculatorService.calculateAmortizationSchedule(
                loan,
              ).payments.isNotEmpty
              ? LoanCalculatorService.calculateAmortizationSchedule(
                  loan,
                ).payments.first.paymentAmount
              : 0.0
        : 0.0;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isSelected ? 4 : 1,
      child: InkWell(
        onTap: () => _handleLoanTap(loan, loanProvider),
        onLongPress: () => _toggleSelection(loan.id),
        child: Container(
          decoration: isSelected
              ? BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 2,
                  ),
                  borderRadius: BorderRadius.circular(12),
                )
              : null,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        loan.name,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                    ),
                    if (isSelected)
                      Icon(
                        Icons.check_circle,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoChip(
                        context,
                        'Amount',
                        '${loan.currency.symbol}${Formatters.formatCurrency(loan.loanAmount, loan.currency)}',
                        isSelected,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildInfoChip(
                        context,
                        'Monthly Payment',
                        '${loan.currency.symbol}${Formatters.formatCurrency(monthlyPayment, loan.currency)}',
                        isSelected,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoChip(
                        context,
                        'Interest Rate',
                        '${loan.annualInterestRate.toStringAsFixed(2)}%',
                        isSelected,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildInfoChip(
                        context,
                        'Term',
                        '${loan.termInMonths} months',
                        isSelected,
                      ),
                    ),
                  ],
                ),
                if (loan.createdAt != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Created: ${Formatters.formatDate(loan.createdAt!)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.outline,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoChip(
    BuildContext context,
    String label,
    String value,
    bool isSelected,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected
            ? Theme.of(context).colorScheme.primary.withOpacity(0.2)
            : Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Column(
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: isSelected
                  ? Theme.of(context).colorScheme.onPrimaryContainer
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: isSelected
                  ? Theme.of(context).colorScheme.onPrimaryContainer
                  : null,
            ),
          ),
        ],
      ),
    );
  }

  void _handleLoanTap(LoanModel loan, LoanProvider loanProvider) {
    if (_isSelectionMode) {
      _toggleSelection(loan.id);
    } else {
      loanProvider.setCurrentLoan(loan);
      Navigator.of(context).pop(); // Go back to calculator
    }
  }

  void _toggleSelection(String loanId) {
    setState(() {
      if (_selectedLoans.contains(loanId)) {
        _selectedLoans.remove(loanId);
        if (_selectedLoans.isEmpty) {
          _isSelectionMode = false;
        }
      } else {
        _selectedLoans.add(loanId);
        _isSelectionMode = true;
      }
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _selectedLoans.clear();
      _isSelectionMode = false;
    });
  }

  void _enterComparisonMode() {
    setState(() {
      _isSelectionMode = true;
    });
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Select 2 loans to compare'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _compareSelectedLoans() {
    if (_selectedLoans.length == 2) {
      final loanProvider = context.read<LoanProvider>();
      final loans = _selectedLoans
          .map((id) => loanProvider.getSavedLoanById(id))
          .where((loan) => loan != null)
          .cast<LoanModel>()
          .toList();

      if (loans.length == 2) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) =>
                ComparisonScreen(loan1: loans[0], loan2: loans[1]),
          ),
        );
      }
    }
  }

  Future<void> _deleteSelectedLoans() async {
    final confirmed = await _showDeleteConfirmation(
      context,
      _selectedLoans.length,
    );
    if (confirmed) {
      final loanProvider = context.read<LoanProvider>();
      for (final loanId in _selectedLoans) {
        await loanProvider.deleteSavedLoan(loanId);
      }
      _exitSelectionMode();
    }
  }

  Future<void> _deleteLoan(String loanId, LoanProvider loanProvider) async {
    final confirmed = await _showDeleteConfirmation(context, 1);
    if (confirmed) {
      await loanProvider.deleteSavedLoan(loanId);
    }
  }

  void _duplicateLoan(LoanModel loan, LoanProvider loanProvider) {
    final duplicatedLoan = loan.copyWith(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: '${loan.name} (Copy)',
      createdAt: DateTime.now(),
      updatedAt: null,
    );

    loanProvider.setCurrentLoan(duplicatedLoan);
    Navigator.of(context).pop(); // Go back to calculator
  }

  Future<void> _handleMenuAction(String action) async {
    switch (action) {
      case 'clear_all':
        final confirmed = await _showClearAllConfirmation(context);
        if (confirmed) {
          await context.read<LoanProvider>().clearAllSavedLoans();
        }
        break;
      case 'export_all':
        // TODO: Implement export all functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Export all feature coming soon')),
        );
        break;
    }
  }

  Future<bool> _showDeleteConfirmation(BuildContext context, int count) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Delete Loan'),
            content: Text(
              count == 1
                  ? 'Are you sure you want to delete this loan?'
                  : 'Are you sure you want to delete $count loans?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Delete'),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<bool> _showClearAllConfirmation(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Clear All Loans'),
            content: const Text(
              'Are you sure you want to delete all saved loans? This action cannot be undone.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Clear All'),
              ),
            ],
          ),
        ) ??
        false;
  }
}
