import 'package:flutter/material.dart';
import '../l10n/generated/app_localizations.dart';

/// A widget that safely displays localized text with fallback
class LocalizedText extends StatelessWidget {
  final String Function(AppLocalizations) localizedTextGetter;
  final String fallbackText;
  final TextStyle? style;

  const LocalizedText({
    super.key,
    required this.localizedTextGetter,
    required this.fallbackText,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final text = l10n != null ? localizedTextGetter(l10n) : fallbackText;
    
    return Text(text, style: style);
  }
}

/// Helper function to get localized text safely
String getLocalizedText(
  BuildContext context,
  String Function(AppLocalizations) getter,
  String fallback,
) {
  final l10n = AppLocalizations.of(context);
  return l10n != null ? getter(l10n) : fallback;
}
