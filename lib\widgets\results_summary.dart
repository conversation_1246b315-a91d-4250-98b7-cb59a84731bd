import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/loan_provider.dart';
import '../utils/formatters.dart';

/// Widget that displays a summary of loan calculation results
class ResultsSummary extends StatelessWidget {
  const ResultsSummary({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<LoanProvider>(
      builder: (context, loanProvider, child) {
        final loan = loanProvider.currentLoan;

        if (!loan.isValid) {
          return const SizedBox.shrink();
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.analytics,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Calculation Results',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Monthly payment - highlighted
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Monthly Payment',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.onPrimaryContainer,
                                ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            Formatters.formatCurrency(
                              loan.monthlyPayment,
                              loan.currency,
                            ),
                            style: Theme.of(context).textTheme.headlineMedium
                                ?.copyWith(
                                  color: Theme.of(
                                    context,
                                  ).colorScheme.onPrimaryContainer,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ],
                      ),
                      Icon(
                        Icons.payment,
                        size: 32,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Other key metrics
                _buildResultRow(
                  context,
                  'Principal Amount',
                  Formatters.formatCurrency(
                    loan.principalAmount,
                    loan.currency,
                  ),
                  Icons.account_balance,
                ),

                const Divider(),

                _buildResultRow(
                  context,
                  'Total Interest Paid',
                  Formatters.formatCurrency(
                    loan.totalInterestPaid,
                    loan.currency,
                  ),
                  Icons.trending_up,
                  valueColor: Theme.of(context).colorScheme.error,
                ),

                const Divider(),

                _buildResultRow(
                  context,
                  'Total Amount Paid',
                  Formatters.formatCurrency(
                    loan.totalAmountPaid,
                    loan.currency,
                  ),
                  Icons.receipt_long,
                ),

                const Divider(),

                _buildResultRow(
                  context,
                  'Loan Term',
                  Formatters.formatLoanTerm(loan.termInMonths),
                  Icons.schedule,
                ),

                if (loan.downPayment > 0) ...[
                  const Divider(),
                  _buildResultRow(
                    context,
                    'Down Payment',
                    Formatters.formatCurrency(loan.downPayment, loan.currency),
                    Icons.payment,
                    valueColor: Theme.of(context).colorScheme.primary,
                  ),
                ],

                const SizedBox(height: 16),

                // Interest to principal ratio
                _buildRatioIndicator(context, loan),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildResultRow(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(label, style: Theme.of(context).textTheme.bodyMedium),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatioIndicator(BuildContext context, loan) {
    final interestRatio = loan.totalInterestPaid / loan.totalAmountPaid;
    final principalRatio = 1 - interestRatio;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Payment Breakdown',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        const SizedBox(height: 8),

        // Visual indicator
        Container(
          height: 8,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(4)),
          child: Row(
            children: [
              Expanded(
                flex: (principalRatio * 100).round(),
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(4),
                      bottomLeft: Radius.circular(4),
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: (interestRatio * 100).round(),
                child: Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.error,
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(4),
                      bottomRight: Radius.circular(4),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 8),

        // Legend
        Row(
          children: [
            _buildLegendItem(
              context,
              'Principal',
              Formatters.formatRatio(principalRatio.toDouble()),
              Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 16),
            _buildLegendItem(
              context,
              'Interest',
              Formatters.formatRatio(interestRatio.toDouble()),
              Theme.of(context).colorScheme.error,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLegendItem(
    BuildContext context,
    String label,
    String percentage,
    Color color,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 4),
        Text(
          '$label ($percentage)',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }
}
