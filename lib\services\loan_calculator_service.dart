import '../models/loan_model.dart';
import '../models/payment_schedule.dart';

/// Service class for performing loan calculations
class LoanCalculatorService {
  /// Calculates the complete amortization schedule for a loan
  static PaymentSchedule calculateAmortizationSchedule(LoanModel loan) {
    if (!loan.isValid) {
      throw ArgumentError('Invalid loan parameters');
    }

    final List<PaymentScheduleItem> payments = [];
    double remainingBalance = loan.principalAmount;
    final monthlyPayment = loan.monthlyPayment;
    final monthlyRate = loan.monthlyInterestRate;
    DateTime currentDate = DateTime.now();

    for (int paymentNumber = 1; paymentNumber <= loan.termInMonths; paymentNumber++) {
      // Calculate interest for this payment
      final interestAmount = remainingBalance * monthlyRate;
      
      // Calculate principal for this payment
      double principalAmount = monthlyPayment - interestAmount;
      
      // Ensure we don't pay more principal than remaining balance
      if (principalAmount > remainingBalance) {
        principalAmount = remainingBalance;
      }
      
      // Update remaining balance
      remainingBalance -= principalAmount;
      
      // Ensure remaining balance doesn't go negative due to floating point precision
      if (remainingBalance < 0.01) {
        remainingBalance = 0.0;
      }

      // Calculate payment date (first payment is next month)
      final paymentDate = DateTime(
        currentDate.year,
        currentDate.month + paymentNumber,
        currentDate.day,
      );

      // Create payment item
      final payment = PaymentScheduleItem(
        paymentNumber: paymentNumber,
        paymentAmount: principalAmount + interestAmount,
        principalAmount: principalAmount,
        interestAmount: interestAmount,
        remainingBalance: remainingBalance,
        paymentDate: paymentDate,
      );

      payments.add(payment);

      // Break if loan is fully paid
      if (remainingBalance <= 0) {
        break;
      }
    }

    // Calculate totals
    final totalInterestPaid = payments.fold(
      0.0,
      (sum, payment) => sum + payment.interestAmount,
    );
    final totalAmountPaid = payments.fold(
      0.0,
      (sum, payment) => sum + payment.paymentAmount,
    );

    return PaymentSchedule(
      payments: payments,
      totalInterestPaid: totalInterestPaid,
      totalAmountPaid: totalAmountPaid,
      totalPayments: payments.length,
    );
  }

  /// Calculates the loan amount needed for a desired monthly payment
  static double calculateLoanAmountForPayment({
    required double desiredMonthlyPayment,
    required double annualInterestRate,
    required int termInMonths,
    double downPayment = 0.0,
  }) {
    if (desiredMonthlyPayment <= 0 || termInMonths <= 0) {
      return 0.0;
    }

    final monthlyRate = annualInterestRate / 100 / 12;

    if (monthlyRate == 0) {
      // If no interest, principal = payment * number of payments
      return (desiredMonthlyPayment * termInMonths) + downPayment;
    }

    // Reverse amortization formula: P = M * [(1+r)^n - 1] / [r(1+r)^n]
    final numerator = pow(1 + monthlyRate, termInMonths) - 1;
    final denominator = monthlyRate * pow(1 + monthlyRate, termInMonths);
    
    final principalAmount = desiredMonthlyPayment * (numerator / denominator);
    return principalAmount + downPayment;
  }

  /// Calculates the interest rate needed for a desired monthly payment
  static double calculateInterestRateForPayment({
    required double loanAmount,
    required double desiredMonthlyPayment,
    required int termInMonths,
    double downPayment = 0.0,
  }) {
    final principalAmount = loanAmount - downPayment;
    
    if (principalAmount <= 0 || desiredMonthlyPayment <= 0 || termInMonths <= 0) {
      return 0.0;
    }

    // If payment equals principal/term, then rate is 0
    if (desiredMonthlyPayment == principalAmount / termInMonths) {
      return 0.0;
    }

    // Use binary search to find the interest rate
    double lowRate = 0.0;
    double highRate = 50.0; // 50% annual rate as upper bound
    double tolerance = 0.0001;
    int maxIterations = 100;

    for (int i = 0; i < maxIterations; i++) {
      double testRate = (lowRate + highRate) / 2;
      double testPayment = _calculateMonthlyPayment(
        principalAmount,
        testRate,
        termInMonths,
      );

      if ((testPayment - desiredMonthlyPayment).abs() < tolerance) {
        return testRate;
      }

      if (testPayment < desiredMonthlyPayment) {
        lowRate = testRate;
      } else {
        highRate = testRate;
      }
    }

    return (lowRate + highRate) / 2;
  }

  /// Helper method to calculate monthly payment for a given annual rate
  static double _calculateMonthlyPayment(
    double principal,
    double annualRate,
    int termInMonths,
  ) {
    if (principal <= 0 || termInMonths <= 0) return 0.0;
    
    final monthlyRate = annualRate / 100 / 12;
    
    if (monthlyRate == 0) {
      return principal / termInMonths;
    }

    final numerator = monthlyRate * pow(1 + monthlyRate, termInMonths);
    final denominator = pow(1 + monthlyRate, termInMonths) - 1;

    return principal * (numerator / denominator);
  }

  /// Compares two loans and returns a comparison summary
  static Map<String, dynamic> compareLoans(LoanModel loan1, LoanModel loan2) {
    return {
      'loan1': {
        'monthlyPayment': loan1.monthlyPayment,
        'totalInterest': loan1.totalInterestPaid,
        'totalAmount': loan1.totalAmountPaid,
      },
      'loan2': {
        'monthlyPayment': loan2.monthlyPayment,
        'totalInterest': loan2.totalInterestPaid,
        'totalAmount': loan2.totalAmountPaid,
      },
      'differences': {
        'monthlyPayment': loan2.monthlyPayment - loan1.monthlyPayment,
        'totalInterest': loan2.totalInterestPaid - loan1.totalInterestPaid,
        'totalAmount': loan2.totalAmountPaid - loan1.totalAmountPaid,
      },
    };
  }
}

// Helper function for power calculation (same as in loan_model.dart)
double pow(double base, int exponent) {
  if (exponent == 0) return 1.0;
  if (exponent == 1) return base;
  
  double result = 1.0;
  for (int i = 0; i < exponent; i++) {
    result *= base;
  }
  return result;
}
