import 'package:json_annotation/json_annotation.dart';
import 'loan_type.dart';
import 'dart:math' as math;
import 'currency.dart';
import 'payment_schedule.dart';

part 'loan_model.g.dart';

/// Represents a loan with all its parameters and calculated results
@JsonSerializable()
class LoanModel {
  final String id;
  final String name;
  final LoanType loanType;
  final double loanAmount;
  final double annualInterestRate;
  final int termInMonths;
  final double downPayment;
  final Currency currency;
  final DateTime createdAt;
  final DateTime? updatedAt;

  // Calculated fields (not stored, computed on demand)
  @JsonKey(includeFromJson: false, includeToJson: false)
  PaymentSchedule? _paymentSchedule;

  LoanModel({
    required this.id,
    required this.name,
    required this.loanType,
    required this.loanAmount,
    required this.annualInterestRate,
    required this.termInMonths,
    this.downPayment = 0.0,
    this.currency = Currency.usd,
    required this.createdAt,
    this.updatedAt,
  });

  factory LoanModel.fromJson(Map<String, dynamic> json) =>
      _$LoanModelFromJson(json);

  Map<String, dynamic> toJson() => _$LoanModelToJson(this);

  /// Creates a new loan with default values
  factory LoanModel.create({
    required String name,
    required LoanType loanType,
    Currency currency = Currency.usd,
  }) {
    return LoanModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      loanType: loanType,
      loanAmount: 0.0,
      annualInterestRate: 0.0,
      termInMonths: 12,
      downPayment: 0.0,
      currency: currency,
      createdAt: DateTime.now(),
    );
  }

  /// Returns the principal amount (loan amount minus down payment)
  double get principalAmount => loanAmount - downPayment;

  /// Returns the monthly interest rate
  double get monthlyInterestRate => annualInterestRate / 100 / 12;

  /// Returns the term in years
  double get termInYears => termInMonths / 12;

  /// Calculates the monthly payment amount using the standard amortization formula
  double get monthlyPayment {
    if (principalAmount <= 0 || termInMonths <= 0) {
      return 0.0;
    }

    final monthlyRate = monthlyInterestRate;
    final numPayments = termInMonths;

    if (monthlyRate == 0 || annualInterestRate == 0) {
      // If no interest, just divide principal by number of payments
      return principalAmount / numPayments;
    }

    // Standard amortization formula: M = P * [r(1+r)^n] / [(1+r)^n - 1]
    final double baseForPow = 1 + monthlyRate;
    final double rPlus1PowN = math.pow(baseForPow, numPayments).toDouble();

    final numerator = monthlyRate * rPlus1PowN;
    final denominator = rPlus1PowN - 1;

    if (denominator == 0.0) {
      // This can happen if rPlus1PowN is 1 (e.g. rate is 0, or base is -1 and numPayments is even)
      // The rate == 0 case is handled above, so this catches other edge cases.
      // Return 0.0 or handle as an error, consistent with other checks.
      return 0.0;
    }

    return principalAmount * (numerator / denominator);
  }

  /// Calculates the total amount paid over the life of the loan
  double get totalAmountPaid => monthlyPayment * termInMonths;

  /// Calculates the total interest paid over the life of the loan
  double get totalInterestPaid => totalAmountPaid - principalAmount;

  /// Returns true if the loan has valid parameters for calculation
  bool get isValid {
    return loanAmount > 0 &&
        annualInterestRate >= 0 &&
        termInMonths > 0 &&
        downPayment >= 0 &&
        downPayment < loanAmount;
  }

  /// Creates a copy of this loan with updated values
  LoanModel copyWith({
    String? id,
    String? name,
    LoanType? loanType,
    double? loanAmount,
    double? annualInterestRate,
    int? termInMonths,
    double? downPayment,
    Currency? currency,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LoanModel(
      id: id ?? this.id,
      name: name ?? this.name,
      loanType: loanType ?? this.loanType,
      loanAmount: loanAmount ?? this.loanAmount,
      annualInterestRate: annualInterestRate ?? this.annualInterestRate,
      termInMonths: termInMonths ?? this.termInMonths,
      downPayment: downPayment ?? this.downPayment,
      currency: currency ?? this.currency,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'LoanModel(id: $id, name: $name, type: $loanType, '
        'amount: ${currency.formatAmount(loanAmount)}, '
        'rate: ${annualInterestRate.toStringAsFixed(2)}%, '
        'term: $termInMonths months)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LoanModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Custom pow function removed, using math.pow instead.
