import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/loan_provider.dart';
import '../providers/settings_provider.dart';
import '../models/loan_type.dart';
import '../models/currency.dart';
import '../l10n/generated/app_localizations.dart';
import '../utils/currency_utils.dart';
import '../utils/formatters.dart';

/// Widget for inputting loan parameters
class LoanInputForm extends StatefulWidget {
  const LoanInputForm({super.key});

  @override
  State<LoanInputForm> createState() => _LoanInputFormState();
}

class _LoanInputFormState extends State<LoanInputForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _rateController = TextEditingController();
  final _termController = TextEditingController();
  final _downPaymentController = TextEditingController();

  bool _termInYears = true;

  @override
  void initState() {
    super.initState();
    _loadCurrentLoan();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _rateController.dispose();
    _termController.dispose();
    _downPaymentController.dispose();
    super.dispose();
  }

  void _loadCurrentLoan() {
    final loan = context.read<LoanProvider>().currentLoan;
    _nameController.text = loan.name;
    _amountController.text = loan.loanAmount > 0
        ? loan.loanAmount.toString()
        : '';
    _rateController.text = loan.annualInterestRate > 0
        ? loan.annualInterestRate.toString()
        : '';
    _termController.text = loan.termInMonths > 0
        ? (_termInYears
              ? (loan.termInMonths / 12).toString()
              : loan.termInMonths.toString())
        : '';
    _downPaymentController.text = loan.downPayment > 0
        ? loan.downPayment.toString()
        : '';
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);

    return Consumer2<LoanProvider, SettingsProvider>(
      builder: (context, loanProvider, settingsProvider, child) {
        final loan = loanProvider.currentLoan;

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n?.loanDetails ?? 'Loan Details',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),

                  // Loan name
                  TextFormField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: l10n?.loanName ?? 'Loan Name',
                      hintText:
                          l10n?.enterLoanName ?? 'Enter a name for this loan',
                      prefixIcon: const Icon(Icons.label),
                    ),
                    onChanged: (value) {
                      loanProvider.updateLoan(name: value);
                    },
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return l10n?.pleaseEnterLoanName ??
                            'Please enter a loan name';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // Loan type and currency row
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<LoanType>(
                          value: loan.loanType,
                          decoration: InputDecoration(
                            labelText: l10n.loanType,
                            prefixIcon: const Icon(Icons.category),
                          ),
                          items: LoanType.values.map((type) {
                            return DropdownMenuItem(
                              value: type,
                              child: Text(_getLoanTypeDisplayName(l10n, type)),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              loanProvider.updateLoan(loanType: value);
                              _updateDownPaymentVisibility(value);
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DropdownButtonFormField<Currency>(
                          value: loan.currency,
                          decoration: InputDecoration(
                            labelText: l10n.currency,
                            prefixIcon: const Icon(Icons.attach_money),
                          ),
                          items: Currency.values.map((currency) {
                            return DropdownMenuItem(
                              value: currency,
                              child: Row(
                                children: [
                                  Text(CurrencyUtils.getCurrencyFlag(currency)),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      currency.code,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              loanProvider.updateLoan(currency: value);
                            }
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Loan amount
                  TextFormField(
                    controller: _amountController,
                    decoration: InputDecoration(
                      labelText: l10n.loanAmount,
                      hintText: l10n.enterLoanAmount,
                      prefixIcon: const Icon(Icons.monetization_on),
                      prefixText: loan.currency.symbol,
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    onChanged: (value) {
                      final amount = double.tryParse(value) ?? 0.0;
                      loanProvider.updateLoan(loanAmount: amount);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return l10n.pleaseEnterLoanAmount;
                      }
                      final amount = double.tryParse(value);
                      if (amount == null || amount <= 0) {
                        return l10n.pleaseEnterValidAmount;
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // Interest rate
                  TextFormField(
                    controller: _rateController,
                    decoration: InputDecoration(
                      labelText: l10n.annualInterestRate,
                      hintText: l10n.enterInterestRate,
                      prefixIcon: const Icon(Icons.percent),
                      suffixText: '%',
                      helperText:
                          'Typical range: ${loan.loanType.typicalRateRange}',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    onChanged: (value) {
                      final rate = double.tryParse(value) ?? 0.0;
                      loanProvider.updateLoan(annualInterestRate: rate);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return l10n.pleaseEnterInterestRate;
                      }
                      final rate = double.tryParse(value);
                      if (rate == null || rate < 0 || rate > 100) {
                        return l10n.pleaseEnterValidRate;
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // Loan term
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _termController,
                              decoration: InputDecoration(
                                labelText: l10n.loanTerm,
                                hintText: l10n.enterLoanTerm,
                                prefixIcon: const Icon(Icons.schedule),
                                suffixText: _termInYears
                                    ? l10n.years
                                    : l10n.months,
                                helperText:
                                    'Typical range: ${loan.loanType.typicalTermRange}',
                              ),
                              keyboardType:
                                  const TextInputType.numberWithOptions(
                                    decimal: true,
                                  ),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                  RegExp(r'^\d*\.?\d*'),
                                ),
                              ],
                              onChanged: (value) {
                                final term = double.tryParse(value) ?? 0.0;
                                final termInMonths = _termInYears
                                    ? (term * 12).round()
                                    : term.round();
                                loanProvider.updateLoan(
                                  termInMonths: termInMonths,
                                );
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return l10n.pleaseEnterLoanTerm;
                                }
                                final term = double.tryParse(value);
                                if (term == null || term <= 0) {
                                  return l10n.pleaseEnterValidTerm;
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          SegmentedButton<bool>(
                            segments: [
                              ButtonSegment(
                                value: true,
                                label: Text(l10n.years),
                              ),
                              ButtonSegment(
                                value: false,
                                label: Text(l10n.months),
                              ),
                            ],
                            selected: {_termInYears},
                            onSelectionChanged: (selection) {
                              setState(() {
                                _termInYears = selection.first;
                                _updateTermController();
                              });
                            },
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Down payment (conditional)
                  if (loan.loanType.requiresDownPayment || loan.downPayment > 0)
                    TextFormField(
                      controller: _downPaymentController,
                      decoration: InputDecoration(
                        labelText: l10n.downPayment,
                        hintText: l10n.enterDownPayment,
                        prefixIcon: const Icon(Icons.payment),
                        prefixText: loan.currency.symbol,
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*'),
                        ),
                      ],
                      onChanged: (value) {
                        final downPayment = double.tryParse(value) ?? 0.0;
                        loanProvider.updateLoan(downPayment: downPayment);
                      },
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final downPayment = double.tryParse(value);
                          if (downPayment == null || downPayment < 0) {
                            return l10n.pleaseEnterValidDownPayment;
                          }
                          if (downPayment >= loan.loanAmount) {
                            return l10n.downPaymentMustBeLess;
                          }
                        }
                        return null;
                      },
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _updateDownPaymentVisibility(LoanType loanType) {
    if (!loanType.requiresDownPayment) {
      _downPaymentController.clear();
      context.read<LoanProvider>().updateLoan(downPayment: 0.0);
    }
  }

  void _updateTermController() {
    final loan = context.read<LoanProvider>().currentLoan;
    if (loan.termInMonths > 0) {
      final value = _termInYears
          ? loan.termInMonths / 12
          : loan.termInMonths.toDouble();
      _termController.text = value.toString();
    }
  }

  String _getLoanTypeDisplayName(AppLocalizations? l10n, LoanType type) {
    switch (type) {
      case LoanType.personal:
        return l10n?.personalLoan ?? 'Personal Loan';
      case LoanType.mortgage:
        return l10n?.mortgage ?? 'Mortgage';
      case LoanType.auto:
        return l10n?.autoLoan ?? 'Auto Loan';
      case LoanType.business:
        return l10n?.businessLoan ?? 'Business Loan';
    }
  }
}
