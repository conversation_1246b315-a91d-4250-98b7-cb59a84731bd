import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../providers/loan_provider.dart';
import '../providers/settings_provider.dart';
import '../models/loan_type.dart';
import '../models/currency.dart';
import '../utils/formatters.dart';

/// Widget for inputting loan parameters
class LoanInputForm extends StatefulWidget {
  const LoanInputForm({super.key});

  @override
  State<LoanInputForm> createState() => _LoanInputFormState();
}

class _LoanInputFormState extends State<LoanInputForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _amountController = TextEditingController();
  final _rateController = TextEditingController();
  final _termController = TextEditingController();
  final _downPaymentController = TextEditingController();

  bool _termInYears = true;

  @override
  void initState() {
    super.initState();
    _loadCurrentLoan();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _rateController.dispose();
    _termController.dispose();
    _downPaymentController.dispose();
    super.dispose();
  }

  void _loadCurrentLoan() {
    final loan = context.read<LoanProvider>().currentLoan;
    _nameController.text = loan.name;
    _amountController.text = loan.loanAmount > 0
        ? loan.loanAmount.toString()
        : '';
    _rateController.text = loan.annualInterestRate > 0
        ? loan.annualInterestRate.toString()
        : '';
    _termController.text = loan.termInMonths > 0
        ? (_termInYears
              ? (loan.termInMonths / 12).toString()
              : loan.termInMonths.toString())
        : '';
    _downPaymentController.text = loan.downPayment > 0
        ? loan.downPayment.toString()
        : '';
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<LoanProvider, SettingsProvider>(
      builder: (context, loanProvider, settingsProvider, child) {
        final loan = loanProvider.currentLoan;

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Loan Details',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),

                  // Loan name
                  TextFormField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'Loan Name',
                      hintText: 'Enter a name for this loan',
                      prefixIcon: Icon(Icons.label),
                    ),
                    onChanged: (value) {
                      loanProvider.updateLoan(name: value);
                    },
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter a loan name';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // Loan type and currency row
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<LoanType>(
                          value: loan.loanType,
                          decoration: const InputDecoration(
                            labelText: 'Loan Type',
                            prefixIcon: Icon(Icons.category),
                          ),
                          items: LoanType.values.map((type) {
                            return DropdownMenuItem(
                              value: type,
                              child: Text(type.displayName),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              loanProvider.updateLoan(loanType: value);
                              _updateDownPaymentVisibility(value);
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: DropdownButtonFormField<Currency>(
                          value: loan.currency,
                          decoration: const InputDecoration(
                            labelText: 'Currency',
                            prefixIcon: Icon(Icons.attach_money),
                          ),
                          items: Currency.values.map((currency) {
                            return DropdownMenuItem(
                              value: currency,
                              child: Text(
                                currency.code,
                                overflow: TextOverflow.ellipsis,
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              loanProvider.updateLoan(currency: value);
                            }
                          },
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Loan amount
                  TextFormField(
                    controller: _amountController,
                    decoration: InputDecoration(
                      labelText: 'Loan Amount',
                      hintText: 'Enter loan amount',
                      prefixIcon: Icon(Icons.monetization_on),
                      prefixText: loan.currency.symbol,
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    onChanged: (value) {
                      final amount = double.tryParse(value) ?? 0.0;
                      loanProvider.updateLoan(loanAmount: amount);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter loan amount';
                      }
                      final amount = double.tryParse(value);
                      if (amount == null || amount <= 0) {
                        return 'Please enter a valid amount';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // Interest rate
                  TextFormField(
                    controller: _rateController,
                    decoration: InputDecoration(
                      labelText: 'Annual Interest Rate',
                      hintText: 'Enter interest rate',
                      prefixIcon: const Icon(Icons.percent),
                      suffixText: '%',
                      helperText:
                          'Typical range: ${loan.loanType.typicalRateRange}',
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                    ],
                    onChanged: (value) {
                      final rate = double.tryParse(value) ?? 0.0;
                      loanProvider.updateLoan(annualInterestRate: rate);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter interest rate';
                      }
                      final rate = double.tryParse(value);
                      if (rate == null || rate < 0 || rate > 100) {
                        return 'Please enter a valid rate (0-100%)';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // Loan term
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _termController,
                              decoration: InputDecoration(
                                labelText: 'Loan Term',
                                hintText: 'Enter loan term',
                                prefixIcon: const Icon(Icons.schedule),
                                suffixText: _termInYears ? 'years' : 'months',
                                helperText:
                                    'Typical range: ${loan.loanType.typicalTermRange}',
                              ),
                              keyboardType:
                                  const TextInputType.numberWithOptions(
                                    decimal: true,
                                  ),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                  RegExp(r'^\d*\.?\d*'),
                                ),
                              ],
                              onChanged: (value) {
                                final term = double.tryParse(value) ?? 0.0;
                                final termInMonths = _termInYears
                                    ? (term * 12).round()
                                    : term.round();
                                loanProvider.updateLoan(
                                  termInMonths: termInMonths,
                                );
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter loan term';
                                }
                                final term = double.tryParse(value);
                                if (term == null || term <= 0) {
                                  return 'Please enter a valid term';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          SegmentedButton<bool>(
                            segments: const [
                              ButtonSegment(value: true, label: Text('Years')),
                              ButtonSegment(
                                value: false,
                                label: Text('Months'),
                              ),
                            ],
                            selected: {_termInYears},
                            onSelectionChanged: (selection) {
                              setState(() {
                                _termInYears = selection.first;
                                _updateTermController();
                              });
                            },
                          ),
                        ],
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Down payment (conditional)
                  if (loan.loanType.requiresDownPayment || loan.downPayment > 0)
                    TextFormField(
                      controller: _downPaymentController,
                      decoration: InputDecoration(
                        labelText: 'Down Payment (Optional)',
                        hintText: 'Enter down payment',
                        prefixIcon: const Icon(Icons.payment),
                        prefixText: loan.currency.symbol,
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d*\.?\d*'),
                        ),
                      ],
                      onChanged: (value) {
                        final downPayment = double.tryParse(value) ?? 0.0;
                        loanProvider.updateLoan(downPayment: downPayment);
                      },
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final downPayment = double.tryParse(value);
                          if (downPayment == null || downPayment < 0) {
                            return 'Please enter a valid down payment';
                          }
                          if (downPayment >= loan.loanAmount) {
                            return 'Down payment must be less than loan amount';
                          }
                        }
                        return null;
                      },
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _updateDownPaymentVisibility(LoanType loanType) {
    if (!loanType.requiresDownPayment) {
      _downPaymentController.clear();
      context.read<LoanProvider>().updateLoan(downPayment: 0.0);
    }
  }

  void _updateTermController() {
    final loan = context.read<LoanProvider>().currentLoan;
    if (loan.termInMonths > 0) {
      final value = _termInYears
          ? loan.termInMonths / 12
          : loan.termInMonths.toDouble();
      _termController.text = value.toString();
    }
  }
}
