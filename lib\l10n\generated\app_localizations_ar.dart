// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'حاسبة القروض المصرفية';

  @override
  String get calculator => 'الحاسبة';

  @override
  String get savedLoans => 'القروض المحفوظة';

  @override
  String get settings => 'الإعدادات';

  @override
  String get loanCalculator => 'حاسبة القروض';

  @override
  String get loanDetails => 'تفاصيل القرض';

  @override
  String get loanName => 'اسم القرض';

  @override
  String get enterLoanName => 'أدخل اسماً لهذا القرض';

  @override
  String get pleaseEnterLoanName => 'يرجى إدخال اسم القرض';

  @override
  String get loanType => 'نوع القرض';

  @override
  String get currency => 'العملة';

  @override
  String get loanAmount => 'مبلغ القرض';

  @override
  String get enterLoanAmount => 'أدخل مبلغ القرض';

  @override
  String get pleaseEnterLoanAmount => 'يرجى إدخال مبلغ القرض';

  @override
  String get pleaseEnterValidAmount => 'يرجى إدخال مبلغ صحيح';

  @override
  String get annualInterestRate => 'معدل الفائدة السنوي';

  @override
  String get enterInterestRate => 'أدخل معدل الفائدة';

  @override
  String get pleaseEnterInterestRate => 'يرجى إدخال معدل الفائدة';

  @override
  String get pleaseEnterValidRate => 'يرجى إدخال معدل صحيح (0-100%)';

  @override
  String get loanTerm => 'مدة القرض';

  @override
  String get enterLoanTerm => 'أدخل مدة القرض';

  @override
  String get pleaseEnterLoanTerm => 'يرجى إدخال مدة القرض';

  @override
  String get pleaseEnterValidTerm => 'يرجى إدخال مدة صحيحة';

  @override
  String get years => 'سنوات';

  @override
  String get months => 'شهور';

  @override
  String get downPayment => 'الدفعة المقدمة (اختيارية)';

  @override
  String get enterDownPayment => 'أدخل الدفعة المقدمة';

  @override
  String get pleaseEnterValidDownPayment => 'يرجى إدخال دفعة مقدمة صحيحة';

  @override
  String get downPaymentMustBeLess =>
      'يجب أن تكون الدفعة المقدمة أقل من مبلغ القرض';

  @override
  String get calculationResults => 'نتائج الحساب';

  @override
  String get monthlyPayment => 'القسط الشهري';

  @override
  String get principalAmount => 'المبلغ الأساسي';

  @override
  String get totalInterestPaid => 'إجمالي الفوائد المدفوعة';

  @override
  String get totalAmountPaid => 'إجمالي المبلغ المدفوع';

  @override
  String get paymentBreakdown => 'تفكيك الدفعات';

  @override
  String get principal => 'الأصل';

  @override
  String get interest => 'الفوائد';

  @override
  String get viewDetails => 'عرض التفاصيل';

  @override
  String get saveLoan => 'حفظ القرض';

  @override
  String get duplicate => 'نسخ';

  @override
  String get newLoan => 'قرض جديد';

  @override
  String get loanSavedSuccessfully => 'تم حفظ القرض بنجاح!';

  @override
  String get failedToSaveLoan => 'فشل في حفظ القرض';

  @override
  String get loanDuplicated => 'تم نسخ القرض';

  @override
  String get newLoanCreated => 'تم إنشاء قرض جديد';

  @override
  String get personalLoan => 'قرض شخصي';

  @override
  String get mortgage => 'قرض عقاري';

  @override
  String get autoLoan => 'قرض سيارة';

  @override
  String get businessLoan => 'قرض تجاري';

  @override
  String get noSavedLoans => 'لا توجد قروض محفوظة';

  @override
  String get calculateLoanAndSave => 'احسب قرضاً واحفظه لتراه هنا';

  @override
  String get compareLoans => 'مقارنة القروض';

  @override
  String get clearAll => 'مسح الكل';

  @override
  String get exportAll => 'تصدير الكل';

  @override
  String get delete => 'حذف';

  @override
  String get deleteLoan => 'حذف القرض';

  @override
  String get areYouSureDeleteLoan => 'هل أنت متأكد من حذف هذا القرض؟';

  @override
  String areYouSureDeleteLoans(int count) {
    return 'هل أنت متأكد من حذف $count قروض؟';
  }

  @override
  String get cancel => 'إلغاء';

  @override
  String get currencyAndDisplay => 'العملة والعرض';

  @override
  String get defaultCurrency => 'العملة الافتراضية';

  @override
  String get decimalPlaces => 'المنازل العشرية';

  @override
  String get display => 'العرض';

  @override
  String get darkMode => 'الوضع المظلم';

  @override
  String get useDarkTheme => 'استخدام المظهر المظلم';

  @override
  String get detailedResults => 'النتائج التفصيلية';

  @override
  String get showDetailedResults => 'عرض نتائج الحساب التفصيلية';

  @override
  String get calculations => 'الحسابات';

  @override
  String get autoSaveCalculations => 'الحفظ التلقائي للحسابات';

  @override
  String get automaticallySave => 'حفظ حسابات القروض تلقائياً';

  @override
  String get dataManagement => 'إدارة البيانات';

  @override
  String get exportData => 'تصدير البيانات';

  @override
  String get exportAllSavedLoans => 'تصدير جميع القروض المحفوظة';

  @override
  String get importData => 'استيراد البيانات';

  @override
  String get importSavedLoans => 'استيراد القروض المحفوظة';

  @override
  String get clearAllData => 'مسح جميع البيانات';

  @override
  String get deleteAllSavedLoans => 'حذف جميع القروض المحفوظة والإعدادات';

  @override
  String get about => 'حول التطبيق';

  @override
  String get appVersion => 'إصدار التطبيق';

  @override
  String get helpAndSupport => 'المساعدة والدعم';

  @override
  String get privacyPolicy => 'سياسة الخصوصية';

  @override
  String get resetToDefaults => 'إعادة تعيين للافتراضي';

  @override
  String get language => 'اللغة';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get english => 'English';

  @override
  String get arabic => 'العربية';

  @override
  String get summary => 'الملخص';

  @override
  String get schedule => 'الجدول';

  @override
  String get quickStatistics => 'إحصائيات سريعة';

  @override
  String get firstPayment => 'الدفعة الأولى';

  @override
  String get finalPayment => 'الدفعة الأخيرة';

  @override
  String get interestRate => 'معدل الفائدة';

  @override
  String get totalPayments => 'إجمالي الدفعات';

  @override
  String get yearlySummary => 'الملخص السنوي';

  @override
  String get shareResults => 'مشاركة النتائج';

  @override
  String get exportAsPdf => 'تصدير كـ PDF';

  @override
  String get monthly => 'شهري';

  @override
  String get yearly => 'سنوي';

  @override
  String get allYears => 'جميع السنوات';

  @override
  String get paymentNumber => 'رقم الدفعة';

  @override
  String get date => 'التاريخ';

  @override
  String get payment => 'الدفعة';

  @override
  String get balance => 'الرصيد';

  @override
  String get payments => 'الدفعات';

  @override
  String get totalPaid => 'إجمالي المدفوع';

  @override
  String get endBalance => 'الرصيد النهائي';

  @override
  String get loanComparison => 'مقارنة القروض';

  @override
  String get quickComparison => 'مقارنة سريعة';

  @override
  String get difference => 'الفرق';

  @override
  String get detailedComparison => 'مقارنة تفصيلية';

  @override
  String get recommendation => 'التوصية';

  @override
  String get bothLoansAreSimilar => 'كلا القرضين متشابهان';

  @override
  String get loanAIsBetter => 'القرض أ أفضل إجمالياً';

  @override
  String get loanBIsBetter => 'القرض ب أفضل إجمالياً';

  @override
  String get note => 'ملاحظة';

  @override
  String get recommendationNote =>
      'هذه التوصية مبنية على الحسابات المالية فقط. يرجى مراعاة عوامل أخرى مثل سمعة المقرض وخدمة العملاء وشروط القرض.';

  @override
  String get created => 'تم الإنشاء';

  @override
  String get amount => 'المبلغ';

  @override
  String get rate => 'المعدل';

  @override
  String get term => 'المدة';

  @override
  String get perMonth => 'شهرياً';

  @override
  String get selectCurrency => 'اختر العملة';

  @override
  String get close => 'إغلاق';

  @override
  String get gotIt => 'فهمت';

  @override
  String get howToUse => 'كيفية الاستخدام';

  @override
  String get howToUseContent =>
      '1. اختر نوع القرض\n2. أدخل مبلغ القرض\n3. حدد معدل الفائدة السنوي\n4. اختر مدة القرض\n5. أضف الدفعة المقدمة إن وجدت\n6. اطلع على النتائج المحسوبة\n7. احفظ أو صدّر الحساب';

  @override
  String get tips => 'نصائح';

  @override
  String get tipsContent =>
      '• استخدم العرض التفصيلي لجدول الاستهلاك\n• احفظ سيناريوهات قروض متعددة للمقارنة\n• صدّر النتائج كـ PDF للمشاركة';

  @override
  String get help => 'المساعدة';

  @override
  String get bankLoanCalculator => 'حاسبة القروض المصرفية';

  @override
  String get helpContent =>
      'هذا التطبيق يساعدك في حساب أقساط القروض ومقارنة خيارات القروض المختلفة وفهم التكلفة الإجمالية للاقتراض.';

  @override
  String get features => 'الميزات';

  @override
  String get featuresContent =>
      '• حساب الأقساط الشهرية\n• عرض جداول الاستهلاك\n• مقارنة قروض متعددة\n• حفظ وإدارة حسابات القروض\n• تصدير النتائج كـ PDF';

  @override
  String get support => 'للدعم، يرجى التواصل معنا على <EMAIL>';

  @override
  String get privacyPolicyContent =>
      'هذا التطبيق يحفظ بيانات حسابات القروض محلياً على جهازك. لا يتم إرسال أي معلومات شخصية أو مالية إلى خوادم خارجية. جميع الحسابات تتم محلياً وبياناتك تبقى خاصة.';

  @override
  String get resetSettings => 'إعادة تعيين الإعدادات';

  @override
  String get resetSettingsConfirm =>
      'إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟';

  @override
  String get reset => 'إعادة تعيين';

  @override
  String get settingsResetToDefaults => 'تم إعادة تعيين الإعدادات للافتراضي';

  @override
  String get clearAllLoans => 'مسح جميع القروض';

  @override
  String get clearAllLoansConfirm =>
      'هل أنت متأكد من حذف جميع القروض المحفوظة؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get allDataCleared => 'تم مسح جميع البيانات بنجاح';

  @override
  String get exportFeatureComingSoon => 'ميزة التصدير قريباً';

  @override
  String get importFeatureComingSoon => 'ميزة الاستيراد قريباً';

  @override
  String get shareComparisonComingSoon => 'ميزة مشاركة المقارنة قريباً';

  @override
  String get exportAllFeatureComingSoon => 'ميزة تصدير الكل قريباً';

  @override
  String get pdfExportedSuccessfully => 'تم تصدير PDF بنجاح';

  @override
  String get errorExportingPdf => 'خطأ في تصدير PDF';

  @override
  String get errorSharingResults => 'خطأ في مشاركة النتائج';

  @override
  String get errorComparingLoans => 'خطأ في مقارنة القروض';

  @override
  String get select2LoansToCompare => 'اختر قرضين للمقارنة';

  @override
  String get selected => 'محدد';

  @override
  String get compare => 'مقارنة';

  @override
  String get deleteSelected => 'حذف المحدد';

  @override
  String get loanA => 'القرض أ';

  @override
  String get loanB => 'القرض ب';

  @override
  String get copy => 'نسخ';
}
